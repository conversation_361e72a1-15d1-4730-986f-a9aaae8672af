# Social Saver Pro

Save social media posts for later viewing.

## Features

- Save posts from multiple social media platforms (Twitter/X, LinkedIn, Reddit)
- View saved posts in a clean dashboard
- Basic search and filtering
- Simple settings page

## Development

### Prerequisites

- Node.js (v16 or higher)
- npm (v7 or higher)

### Setup

1. Clone the repository:
```bash
git clone [your-repo-url]
cd social-saver-pro
```

2. Install dependencies:
```bash
npm install
```

3. Start development server:
```bash
npm run dev
```

4. Load the extension in Chrome/Edge:
   - Open Chrome/Edge
   - Go to Extensions page (chrome://extensions or edge://extensions)
   - Enable "Developer mode"
   - Click "Load unpacked"
   - Select the `dist` directory

### Build

To build the extension for production:

```bash
npm run build
```

The built extension will be in the `dist` directory.

## License

MIT