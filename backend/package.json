{"name": "backend", "version": "1.0.0", "main": "dist/server.js", "scripts": {"build": "tsc && npm run copy-js-files", "copy-js-files": "cp -R src/routes/*.js dist/routes/ && cp -R src/controllers/*.js dist/controllers/ && echo 'Copied JS files'", "start": "if [ \"$RAILWAY_SERVICE_NAME\" = \"worker\" ]; then node dist/worker.js; else node dist/server.js; fi", "worker": "node dist/worker.js", "dev": "nodemon --exec \"node --use-system-ca node_modules/ts-node/dist/bin.js\" src/server.ts", "dev:worker": "nodemon --exec \"node --use-system-ca node_modules/ts-node/dist/bin.js\" src/worker.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"aws-sdk": "^2.1692.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bull": "^4.12.9", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "stripe": "^14.21.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/multer": "^1.4.11", "@types/node": "^22.15.3", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pg": "^8.15.2", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}