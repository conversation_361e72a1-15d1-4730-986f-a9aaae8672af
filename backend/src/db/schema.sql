-- Database schema for Notely.social

-- Users table
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  password VARCHAR(255),
  google_id VARCHAR(255),
  plan VARCHAR(50) DEFAULT 'free',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  last_login TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  stripe_customer_id VARCHAR(255),
  stripe_subscription_id VARCHAR(255)
);

-- Posts table
CREATE TABLE posts (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id),
  original_post_id VARCHAR(255),
  platform VARCHAR(50) NOT NULL,
  author_name VARCHAR(255),
  author_handle VARCHAR(255),
  author_avatar_url VARCHAR(1024),
  author_avatar_s3_key VARCHAR(1024),
  content TEXT,
  alt_text TEXT,
  timestamp VARCHAR(255),
  saved_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  permalink VARCHAR(1024) NOT NULL,
  
  -- Constraints
  UNIQUE(user_id, permalink),
  UNIQUE(user_id, platform, original_post_id) WHERE original_post_id IS NOT NULL
);

-- Media items table
CREATE TABLE media_items (
  id SERIAL PRIMARY KEY,
  post_id INTEGER NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL,
  url VARCHAR(1024) NOT NULL,
  s3_key VARCHAR(1024),
  alt_text TEXT,
  width INTEGER,
  height INTEGER,
  is_primary BOOLEAN DEFAULT false
);

-- Post interactions table
CREATE TABLE post_interactions (
  post_id INTEGER PRIMARY KEY REFERENCES posts(id) ON DELETE CASCADE,
  likes INTEGER DEFAULT 0,
  replies INTEGER DEFAULT 0,
  reposts INTEGER DEFAULT 0
);

-- Categories table
CREATE TABLE categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL UNIQUE,
  user_id INTEGER REFERENCES users(id),
  is_system BOOLEAN DEFAULT false
);

-- Post categories (many-to-many)
CREATE TABLE post_categories (
  post_id INTEGER REFERENCES posts(id) ON DELETE CASCADE,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
  PRIMARY KEY (post_id, category_id)
);

-- Tags table
CREATE TABLE tags (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL UNIQUE,
  user_id INTEGER REFERENCES users(id)
);

-- Post tags (many-to-many)
CREATE TABLE post_tags (
  post_id INTEGER REFERENCES posts(id) ON DELETE CASCADE,
  tag_id INTEGER REFERENCES tags(id) ON DELETE CASCADE,
  PRIMARY KEY (post_id, tag_id)
);

-- AI insights table
CREATE TABLE ai_insights (
  post_id INTEGER PRIMARY KEY REFERENCES posts(id) ON DELETE CASCADE,
  sentiment VARCHAR(50),
  emoji VARCHAR(10),
  context_tags JSONB,
  snap_note TEXT,
  fast_take TEXT,
  content_ideas JSONB,
  analyzed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_posts_user_id ON posts(user_id);
CREATE INDEX idx_posts_platform ON posts(platform);
CREATE INDEX idx_posts_saved_at ON posts(saved_at);
CREATE INDEX idx_media_items_post_id ON media_items(post_id);
CREATE INDEX idx_post_categories_post_id ON post_categories(post_id);
CREATE INDEX idx_post_categories_category_id ON post_categories(category_id);
CREATE INDEX idx_post_tags_post_id ON post_tags(post_id);
CREATE INDEX idx_post_tags_tag_id ON post_tags(tag_id);
