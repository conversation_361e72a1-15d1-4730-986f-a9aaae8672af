import express, { Request, Response, NextFunction, Router, RequestHandler as ExpressRequestHandler } from 'express';
import passport from 'passport';
import jwt from 'jsonwebtoken';
import User, { IUser } from '../models/User'; // Adjust path as needed
import dotenv from 'dotenv';
import { protect, isAdmin } from '../middleware/authMiddleware'; // Import the protect middleware
import Stripe from 'stripe';

dotenv.config();

const router: Router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  console.error('FATAL ERROR: JWT_SECRET is not defined.');
  process.exit(1);
}

// Initialize Stripe without a fixed apiVersion to use account default or latest compatible.
// You can pin a version later if needed e.g. { apiVersion: '2023-10-16' }
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

// --- Helper function to generate JWT ---
const generateToken = (user: IUser) => {
  // Sign the JWT
  return jwt.sign(
    { id: user.id, displayName: user.displayName, email: user.email, googleId: user.googleId }, // Payload
    JWT_SECRET!,
    { expiresIn: '7d' } // Token expires in 7 days
  );
};

// --- Wrapper for async route handlers ---
const asyncHandler = <T extends ExpressRequestHandler>(handler: T): T => {
  return ((req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(handler(req, res, next)).catch(next);
  }) as T;
};

// --- Define handler function separately ---
const registerUser: ExpressRequestHandler = async (req: Request, res: Response, next: NextFunction) => {
  const { email, password, displayName } = req.body;

  // No try...catch needed here as asyncHandler will catch promise rejections
  // Check if user already exists
  const existingUser = await User.findOne({ email: email.toLowerCase() });
  if (existingUser) {
    // Use return to stop execution, send response
    res.status(400).json({ message: 'Email already exists' });
    return;
  }

  // Create new user (password will be hashed by pre-save hook)
  const newUser = new User({
    email: email.toLowerCase(),
    password,
    name: displayName || email.split('@')[0], // Use displayName or derive from email
    displayName: displayName,
    plan: 'free', // Default plan
    // lastLogin will be set by timestamps or on first login via /user/me
  });
  await newUser.save();

  // Generate token and send response
  const token = generateToken(newUser);
  res.status(201).json({ token });
};

// --- Local Registration Route ---
router.post('/register', asyncHandler(registerUser)); // Wrap the handler

// --- Local Login Route ---
router.post('/login', (req: Request, res: Response, next: NextFunction) => {
  passport.authenticate('local', { session: false }, (err: any, user: IUser | false, info: any) => {
    if (err) {
      return next(err);
    }
    if (!user) {
      // If user is false, send the message from the strategy (e.g., 'Invalid email or password')
      res.status(401).json({ message: info?.message || 'Login failed' });
      return;
    }

    // User authenticated successfully, generate token
    const token = generateToken(user);
    res.json({ token });

  })(req, res, next);
});

// --- Google OAuth Initiation Route ---
router.get('/google', (req: Request, res: Response, next: NextFunction) => {
  // Get prompt parameter from request query if it exists
  const prompt = req.query.prompt as string;

  // Configure auth options based on prompt parameter
  const authOptions: any = {
    scope: ['profile', 'email'],
    session: false
  };

  // Add prompt parameter if provided (force account selection)
  if (prompt) {
    authOptions.prompt = prompt;
  }

  passport.authenticate('google', authOptions)(req, res, next);
});

// --- Google OAuth Callback Route ---
router.get('/google/callback', passport.authenticate('google', {
    failureRedirect: '/auth/login/failed',
    session: false
}), async (req, res, next) => {
    const googleProfileUser = req.user as IUser;
    if (!googleProfileUser || !googleProfileUser.googleId) {
      return res.redirect('/auth/login/failed?message=GoogleProfileMissing');
    }

    if (!googleProfileUser.email) {
      console.warn('Google login attempt without email from profile:', googleProfileUser.googleId);
      return res.redirect('/auth/login/failed?message=EmailRequiredFromGoogle');
    }

    try {
      let userInDb = await User.findOne({ googleId: googleProfileUser.googleId });
      if (!userInDb) {
        // Email is guaranteed to be present here due to the check above.
        const userEmail = googleProfileUser.email.toLowerCase();
        const existingEmailUser = await User.findOne({ email: userEmail });

        if (existingEmailUser) {
          // Email already exists (e.g., local account), link Google ID to it.
          existingEmailUser.googleId = googleProfileUser.googleId;
          existingEmailUser.name = existingEmailUser.name || googleProfileUser.displayName || userEmail.split('@')[0];
          existingEmailUser.displayName = googleProfileUser.displayName || existingEmailUser.displayName;
          existingEmailUser.lastLogin = new Date();
          userInDb = await existingEmailUser.save();
        } else {
          // Create new user from Google profile.
          userInDb = await User.create({
            googleId: googleProfileUser.googleId,
            email: userEmail, // Safe to use userEmail here
            name: googleProfileUser.displayName || userEmail.split('@')[0],
            displayName: googleProfileUser.displayName,
            plan: 'free',
            lastLogin: new Date(),
          });
        }
      } else {
        // User exists, update lastLogin and potentially name/displayName if changed.
        userInDb.lastLogin = new Date();
        userInDb.name = googleProfileUser.displayName || userInDb.name;
        userInDb.displayName = googleProfileUser.displayName || userInDb.displayName;
        // Ensure email is updated if it somehow changed (should be rare for Google ID linked accounts)
        if (googleProfileUser.email && userInDb.email !== googleProfileUser.email.toLowerCase()) {
            userInDb.email = googleProfileUser.email.toLowerCase();
        }
        await userInDb.save();
      }

      const token = generateToken(userInDb);
      res.send(
        `<html><head><title>Authentication Success</title></head><body>
        <p>Authentication successful. Please wait...</p>
        <script>
          if (window.opener) {
            window.opener.postMessage({ type: 'AUTH_SUCCESS', token: '${token}' }, '*');
            window.close();
          } else {
            document.body.innerHTML = '<p>Authentication complete. You can close this window.</p><p>Token (for testing): ${token}</p>';
          }
        </script></body></html>`
      );
    } catch (error) {
      console.error("Error in Google OAuth callback user processing:", error);
      if (next) next(error);
      else res.redirect('/auth/login/failed?message=ErrorProcessingGoogleUser');
    }
  }
);

// --- Simple failure route (for redirect example) ---
router.get('/login/failed', (req: Request, res: Response) => {
  const message = req.query.message || 'Google Login Failed';
  res.status(401).json({ message });
});

// --- Example Protected Route ---
// This route requires a valid JWT in the Authorization header (Bearer token)
router.get('/me', protect, asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  // The 'protect' middleware attaches the authenticated user to req.user
  // We only send back non-sensitive information
  const user = req.user as IUser; // Type assertion after middleware ensures user exists
  try {
    // The user object from JWT might be stale, fetch fresh from DB
    const userInDb = await User.findById(user.id);
    if (!userInDb) {
      res.status(404).json({ message: "User not found." });
      return; // Important to return to avoid further execution
    }
    userInDb.lastLogin = new Date();
    await userInDb.save();

    res.json({
      id: userInDb.id,
      name: userInDb.name,
      displayName: userInDb.displayName,
      email: userInDb.email,
      googleId: userInDb.googleId,
      plan: userInDb.plan,
      createdAt: userInDb.createdAt,
      lastLogin: userInDb.lastLogin,
      profileSettings: userInDb.profileSettings,
      stripeCustomerId: userInDb.stripeCustomerId,
      stripeSubscriptionId: userInDb.stripeSubscriptionId,
    });
  } catch (error) {
    // console.error("Error in /me (JWT flow):", error); // Already handled by asyncHandler
    // next(error); // asyncHandler will call next(error)
  }
}));

// Admin: List all users
router.get('/user/all', protect, isAdmin, asyncHandler(async (req, res, next) => {
  const users = await User.find().select('-password -__v -userId'); // Exclude Firebase userId if still in schema
  res.json(users);
}));

// Admin: Manually upgrade user to premium
router.post('/user/upgrade', protect, isAdmin, asyncHandler(async (req, res, next) => {
  // userId in body now refers to MongoDB _id
  const { userId: mongoUserId, plan } = req.body;
  if (!mongoUserId || !['free', 'premium'].includes(plan)) {
    res.status(400).json({ message: 'MongoDB userId and valid plan required' });
    return;
  }
  const userToUpdate = await User.findById(mongoUserId);
  if (!userToUpdate) {
    res.status(404).json({ message: 'User not found' });
    return;
  }
  userToUpdate.plan = plan as 'free' | 'premium';
  await userToUpdate.save();
  res.json({ message: `User ${mongoUserId} upgraded to ${plan}` });
}));

// Create Stripe Checkout session for monthly/yearly plans
router.post('/stripe/create-checkout-session', protect, asyncHandler(async (req, res, next) => {
  const { plan } = req.body;
  const authenticatedUser = req.user as IUser; // From JWT

  if (!authenticatedUser || !authenticatedUser.email) {
    res.status(401).json({ message: 'User not authenticated or email missing.' });
    return;
  }
  const userEmail = authenticatedUser.email;
  const userId = authenticatedUser.id; // MongoDB _id

  // Find user in DB to get/set stripeCustomerId
  let userInDb = await User.findById(userId);
  if (!userInDb) {
      // This should ideally not happen if user is authenticated with a valid JWT
      res.status(404).json({message: "Authenticated user not found in DB for Stripe session."});
      return;
  }

  const priceIdKey = plan === 'yearly' ? 'STRIPE_PRICE_ID_YEARLY' : 'STRIPE_PRICE_ID_MONTHLY';
  const priceId = process.env[priceIdKey];
  if (!priceId) {
    res.status(500).json({ message: `Stripe price ID for ${priceIdKey} not configured` });
    return;
  }
  let stripeCustomerId = userInDb.stripeCustomerId;
  if (!stripeCustomerId) {
    const customer = await stripe.customers.create({ email: userEmail });
    stripeCustomerId = customer.id;
    userInDb.stripeCustomerId = stripeCustomerId;
    await userInDb.save();
  }
  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    mode: 'subscription',
    customer: stripeCustomerId,
    line_items: [{ price: priceId, quantity: 1 }],
    success_url: `${process.env.SERVER_BASE_URL || 'http://localhost:3000'}/auth/payment/success?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.SERVER_BASE_URL || 'http://localhost:3000'}/auth/payment/cancelled?session_id={CHECKOUT_SESSION_ID}`,
    metadata: { userId: userId }, // Pass MongoDB _id as userId in metadata
  });
  res.json({ url: session.url });
}));

// Export the webhook handler separately
export const stripeWebhookHandler: ExpressRequestHandler = async (req, res, next) => {
  const sig = req.headers['stripe-signature'];
  let event;
  try {
    if (!sig) throw new Error('No stripe-signature header');
    if (!process.env.STRIPE_WEBHOOK_SECRET) throw new Error('STRIPE_WEBHOOK_SECRET not set');
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    let message = 'Unknown webhook error';
    if (err instanceof Error) message = err.message;
    console.error('Stripe webhook signature error:', message);
    res.status(400).send(`Webhook Error: ${message}`);
    return;
  }

  try {
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object as Stripe.Checkout.Session;
      // userId in metadata is MongoDB _id
      const mongoUserId = session.metadata?.userId;
      const subscriptionId = session.subscription as string;
      if (mongoUserId && subscriptionId) {
        const user = await User.findById(mongoUserId);
        if (user) {
          user.plan = 'premium';
          user.stripeSubscriptionId = subscriptionId;
          if (session.customer) user.stripeCustomerId = session.customer as string;
          await user.save();
        }
      }
    } else if (event.type === 'customer.subscription.deleted' || event.type === 'customer.subscription.updated') {
      const subscription = event.data.object as Stripe.Subscription;
      // On update, check status. If inactive (e.g., past_due, canceled), set to free.
      const isActive = subscription.status === 'active' || subscription.status === 'trialing';
      const stripeCustomerId = subscription.customer as string;
      const user = await User.findOne({ stripeCustomerId });
      if (user) {
        user.plan = isActive ? 'premium' : 'free';
        user.stripeSubscriptionId = isActive ? subscription.id : undefined;
        await user.save();
      }
    }
    res.json({ received: true });
  } catch (dbError) {
    console.error('Error processing Stripe webhook event:', dbError);
    if (next) next(dbError);
    else res.status(500).json({ message: "Error processing webhook" });
  }
};

// --- Stripe Payment Success/Cancel Redirect Handlers ---
router.get('/payment/success', (req: Request, res: Response) => {
  const clientDashboardUrl = process.env.CLIENT_URL || 'chrome-extension://hnlopcaeidipbmokamhboiooholpecbf/dashboard.html';
  const sessionId = req.query.session_id;
  res.send(`
    <html>
      <head><title>Payment Success</title></head>
      <body>
        <h1>Payment Successful!</h1>
        <p>Your payment was processed successfully.</p>
        <p><a href="${clientDashboardUrl}?payment_status=success${sessionId ? '&checkout_session_id=' + sessionId : ''}" id="returnToApp">Click here to return to Social Saver Pro</a></p>
        <p>If you are not redirected automatically, please copy and paste this link into your address bar or click the link above:</p>
        <p><code>${clientDashboardUrl}?payment_status=success${sessionId ? '&checkout_session_id=' + sessionId : ''}</code></p>
        <script>
          // Optional: A small delay then attempt redirect. Might still be blocked but worth a try.
          // setTimeout(() => { window.location.href = "${clientDashboardUrl}?payment_status=success${sessionId ? '&checkout_session_id=' + sessionId : ''}"; }, 1000);
        </script>
      </body>
    </html>
  `);
});

router.get('/payment/cancelled', (req: Request, res: Response) => {
  const clientDashboardUrl = process.env.CLIENT_URL || 'chrome-extension://hnlopcaeidipbmokamhboiooholpecbf/dashboard.html';
  const sessionId = req.query.session_id;
  res.send(`
    <html>
      <head><title>Payment Cancelled</title></head>
      <body>
        <h1>Payment Cancelled</h1>
        <p>Your payment process was cancelled.</p>
        <p><a href="${clientDashboardUrl}?payment_status=cancelled${sessionId ? '&checkout_session_id=' + sessionId : ''}" id="returnToApp">Click here to return to Social Saver Pro</a></p>
        <p>If you are not redirected automatically, please copy and paste this link into your address bar or click the link above:</p>
        <p><code>${clientDashboardUrl}?payment_status=cancelled${sessionId ? '&checkout_session_id=' + sessionId : ''}</code></p>
      </body>
    </html>
  `);
});

// Remove the webhook route from the main router to handle it separately in server.ts
// router.post('/stripe/webhook', express.raw({ type: 'application/json' }), stripeWebhookHandler);

// @route   GET /api/auth/storage-usage
// @desc    Get user's storage usage and limits
// @access  Private
router.get('/storage-usage', protect, async (req: Request, res: Response) => {
  try {
    const authenticatedUser = req.user as IUser; // Type assertion after protect middleware
    const user = await User.findById(authenticatedUser.id).select('storageUsed storageLimit plan');

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    const usagePercentage = Math.round((user.storageUsed / user.storageLimit) * 100);
    const usedMB = Math.round(user.storageUsed / (1024 * 1024) * 100) / 100; // Round to 2 decimal places
    const limitMB = Math.round(user.storageLimit / (1024 * 1024));

    res.json({
      storageUsed: user.storageUsed,
      storageLimit: user.storageLimit,
      usagePercentage,
      usedMB,
      limitMB,
      plan: user.plan,
      isNearLimit: usagePercentage >= 90,
      isOverLimit: usagePercentage >= 100
    });
  } catch (error) {
    console.error('Error fetching storage usage:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

export default router;