import AWS from 'aws-sdk';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import dotenv from 'dotenv';
import User from '../models/User';

dotenv.config();

// Fix region code - Railway sets it to full name, we need just the code
const REGION_CODE = process.env.AWS_S3_REGION?.includes('eu-north-1') ? 'eu-north-1' : process.env.AWS_S3_REGION;

// Configure AWS SDK with explicit endpoint for eu-north-1 region
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: REGION_CODE
});

const s3 = new AWS.S3({
  region: REGION_CODE,
  endpoint: REGION_CODE === 'eu-north-1'
    ? 'https://s3.eu-north-1.amazonaws.com'
    : undefined,
  s3ForcePathStyle: false,
  signatureVersion: 'v4'
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || '';

// Validate S3 configuration
if (!BUCKET_NAME || !process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY || !process.env.AWS_S3_REGION) {
  console.error('Missing required AWS S3 environment variables. S3 uploads will fail.');
} else {
  console.log(`[S3 Service] Raw region from env: ${process.env.AWS_S3_REGION}`);
  console.log(`[S3 Service] Fixed region code: ${REGION_CODE}`);
  console.log(`[S3 Service] Initialized with bucket: ${BUCKET_NAME}`);
  if (REGION_CODE === 'eu-north-1') {
    console.log(`[S3 Service] Using explicit endpoint for eu-north-1: https://s3.eu-north-1.amazonaws.com`);
  }
}

/**
 * Fetch an image from a URL and return as a buffer
 */
export async function fetchImageAsBuffer(url: string): Promise<{ buffer: Buffer; contentType: string }> {
  try {
    // Set custom headers to mimic a browser request
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
      'Referer': 'https://www.instagram.com/',
      'Origin': 'https://www.instagram.com'
    };

    const response = await axios.get(url, {
      headers,
      responseType: 'arraybuffer',
      timeout: 10000 // 10 second timeout
    });

    return {
      buffer: Buffer.from(response.data, 'binary'),
      contentType: response.headers['content-type'] || 'image/jpeg'
    };
  } catch (error: unknown) {
    console.error(`Error fetching image from ${url}:`, error);
    throw new Error(`Failed to fetch image: ${
      error && typeof error === 'object' && 'message' in error
        ? error.message
        : 'Unknown error'
    }`);
  }
}

/**
 * Check if user has enough storage space for upload
 */
async function checkStorageLimit(userId: string, fileSize: number): Promise<{ allowed: boolean; reason?: string }> {
  try {
    const user = await User.findById(userId);
    if (!user) {
      return { allowed: false, reason: 'User not found' };
    }

    const newUsage = user.storageUsed + fileSize;
    if (newUsage > user.storageLimit) {
      const limitMB = Math.round(user.storageLimit / (1024 * 1024));
      const usedMB = Math.round(user.storageUsed / (1024 * 1024));
      const fileMB = Math.round(fileSize / (1024 * 1024));

      return {
        allowed: false,
        reason: `Storage limit exceeded. Used: ${usedMB}MB, Limit: ${limitMB}MB, File: ${fileMB}MB`
      };
    }

    return { allowed: true };
  } catch (error) {
    console.error('Error checking storage limit:', error);
    return { allowed: false, reason: 'Error checking storage limit' };
  }
}

/**
 * Update user's storage usage
 */
async function updateStorageUsage(userId: string, sizeChange: number): Promise<void> {
  try {
    await User.findByIdAndUpdate(userId, {
      $inc: { storageUsed: sizeChange }
    });
  } catch (error) {
    console.error('Error updating storage usage:', error);
  }
}

/**
 * Upload an image buffer to S3 with storage limit checking
 */
export async function uploadBufferToS3(
  buffer: Buffer,
  contentType: string,
  userId: string,
  type: 'post' | 'avatar',
  originalUrl?: string,
  platform?: string
): Promise<{ key: string; url: string }> {
  try {
    // Check storage limit before upload
    const storageCheck = await checkStorageLimit(userId, buffer.length);
    if (!storageCheck.allowed) {
      console.log(`[S3 Upload] Storage limit check failed for user ${userId}: ${storageCheck.reason}`);
      throw new Error(storageCheck.reason || 'Storage limit exceeded');
    }

    // Determine file extension from content type
    let fileExtension = '.jpg'; // Default
    if (contentType.includes('png')) fileExtension = '.png';
    if (contentType.includes('gif')) fileExtension = '.gif';
    if (contentType.includes('webp')) fileExtension = '.webp';

    // Create a unique S3 key (file path)
    const s3Key = `users/${userId}/${type}s/${uuidv4()}${fileExtension}`;

    // Twitter-specific logging
    if (platform === 'X/Twitter') {
      console.log(`[S3 Upload] Twitter image upload - User: ${userId}, Size: ${Math.round(buffer.length / 1024)}KB, Original URL: ${originalUrl?.substring(0, 50)}...`);
    }

    // Debug: Log the actual S3 configuration being used
    if (platform === 'X/Twitter') {
      console.log(`[S3 Upload] 🔍 S3 client config - Region: ${s3.config.region}, Endpoint: ${s3.config.endpoint}`);
      console.log(`[S3 Upload] 🔍 AWS global config - Region: ${AWS.config.region}`);
    }

    // Upload to S3 (removed ACL to fix AccessControlListNotSupported errors)
    const uploadParams: AWS.S3.PutObjectRequest = {
      Bucket: BUCKET_NAME,
      Key: s3Key,
      Body: buffer,
      ContentType: contentType,
      // ACL removed - bucket policy should handle public access
    };

    const uploadResult = await s3.upload(uploadParams).promise();

    // Update user's storage usage
    await updateStorageUsage(userId, buffer.length);

    // Twitter-specific success logging
    if (platform === 'X/Twitter') {
      console.log(`[S3 Upload] ✅ Twitter image uploaded successfully: ${uploadResult.Location}`);
    } else {
      console.log(`Successfully uploaded to S3: ${uploadResult.Location}`);
    }

    return {
      key: s3Key,
      url: uploadResult.Location
    };
  } catch (error: unknown) {
    // Twitter-specific error logging
    if (platform === 'X/Twitter') {
      console.error(`[S3 Upload] ❌ Twitter image upload failed for user ${userId}:`, error);
    } else {
      console.error('Error uploading to S3:', error);
    }

    throw new Error(`Failed to upload to S3: ${
      error && typeof error === 'object' && 'message' in error
        ? error.message
        : 'Unknown error'
    }`);
  }
}

/**
 * Fetch an image from a URL and upload it to S3
 */
export async function fetchAndUploadImage(
  imageUrl: string,
  userId: string,
  type: 'post' | 'avatar',
  platform?: string
): Promise<{ key: string; url: string }> {
  try {
    const { buffer, contentType } = await fetchImageAsBuffer(imageUrl);
    return await uploadBufferToS3(buffer, contentType, userId, type, imageUrl, platform);
  } catch (error) {
    console.error(`Error in fetchAndUploadImage for ${imageUrl}:`, error);
    throw error;
  }
}

/**
 * Delete an image from S3 and update storage usage
 */
export async function deleteImageFromS3(s3Key: string, userId?: string): Promise<boolean> {
  try {
    // Get file size before deletion if userId is provided
    let fileSize = 0;
    if (userId) {
      try {
        const headParams: AWS.S3.HeadObjectRequest = {
          Bucket: BUCKET_NAME,
          Key: s3Key
        };
        const headResult = await s3.headObject(headParams).promise();
        fileSize = headResult.ContentLength || 0;
      } catch (headError) {
        console.warn(`Could not get file size for ${s3Key}, proceeding with deletion`);
      }
    }

    const deleteParams: AWS.S3.DeleteObjectRequest = {
      Bucket: BUCKET_NAME,
      Key: s3Key
    };

    await s3.deleteObject(deleteParams).promise();

    // Update storage usage if we have userId and file size
    if (userId && fileSize > 0) {
      await updateStorageUsage(userId, -fileSize);
      console.log(`Successfully deleted from S3: ${s3Key} (${Math.round(fileSize / 1024)}KB freed)`);
    } else {
      console.log(`Successfully deleted from S3: ${s3Key}`);
    }

    return true;
  } catch (error) {
    console.error(`Error deleting from S3 (${s3Key}):`, error);
    return false;
  }
}
