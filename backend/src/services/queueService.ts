import Bull from 'bull';
import Redis from 'ioredis';

// Redis connection configuration
let redisConfig: any;
if (process.env.REDIS_URL) {
  redisConfig = process.env.REDIS_URL;
} else {
  redisConfig = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
  };
}

// Create Redis connection
export const redis = new Redis(redisConfig);

// Job data interfaces
export interface ImageProcessingJobData {
  postId: string;
  userId: string;
  platform: string;
  mediaItems: Array<{
    url: string;
    type: 'image' | 'video';
    alt?: string;
    width?: number;
    height?: number;
  }>;
  originalPostData: any; // Full post data for context
}

// Create Bull queue for image processing
export const imageProcessingQueue = new Bull<ImageProcessingJobData>('image-processing', {
  redis: redisConfig,
  defaultJobOptions: {
    removeOnComplete: 10, // Keep last 10 completed jobs
    removeOnFail: 50,     // Keep last 50 failed jobs
    attempts: 3,          // Retry failed jobs up to 3 times
    backoff: {
      type: 'exponential',
      delay: 2000,        // Start with 2 second delay
    },
  },
});

// Queue monitoring and logging
imageProcessingQueue.on('completed', (job, result) => {
  console.log(`[Queue] Image processing job ${job.id} completed for post ${job.data.postId}`);
});

imageProcessingQueue.on('failed', (job, err) => {
  console.error(`[Queue] Image processing job ${job.id} failed for post ${job.data.postId}:`, err.message);
});

imageProcessingQueue.on('stalled', (job) => {
  console.warn(`[Queue] Image processing job ${job.id} stalled for post ${job.data.postId}`);
});

// Helper function to add image processing job
export async function addImageProcessingJob(
  postId: string,
  userId: string,
  platform: string,
  mediaItems: ImageProcessingJobData['mediaItems'],
  originalPostData: any,
  options?: {
    delay?: number;
    priority?: number;
  }
): Promise<Bull.Job<ImageProcessingJobData>> {
  const jobData: ImageProcessingJobData = {
    postId,
    userId,
    platform,
    mediaItems,
    originalPostData,
  };

  console.log(`[Queue] Adding image processing job for post ${postId} (${platform}) with ${mediaItems.length} media items`);

  return imageProcessingQueue.add('process-images', jobData, {
    delay: options?.delay || 0,
    priority: options?.priority || 0,
    // Add job ID for easier tracking
    jobId: `image-processing-${postId}-${Date.now()}`,
  });
}

// Helper function to get queue stats
export async function getQueueStats() {
  const waiting = await imageProcessingQueue.getWaiting();
  const active = await imageProcessingQueue.getActive();
  const completed = await imageProcessingQueue.getCompleted();
  const failed = await imageProcessingQueue.getFailed();

  return {
    waiting: waiting.length,
    active: active.length,
    completed: completed.length,
    failed: failed.length,
  };
}

// Helper function to clean up old jobs
export async function cleanQueue() {
  await imageProcessingQueue.clean(24 * 60 * 60 * 1000, 'completed'); // Remove completed jobs older than 24 hours
  await imageProcessingQueue.clean(7 * 24 * 60 * 60 * 1000, 'failed'); // Remove failed jobs older than 7 days
}

// Graceful shutdown
export async function closeQueue() {
  await imageProcessingQueue.close();
  await redis.disconnect();
}
