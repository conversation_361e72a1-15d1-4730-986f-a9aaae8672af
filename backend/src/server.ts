import express, { Express, Request, Response } from 'express';
import dotenv from 'dotenv';
import path from 'path'; // Import path module
import cors from 'cors'; // <--- Added import
import connectDB from './config/db'; // Import the DB connection function for MongoDB
import passport from './config/passport'; // Import configured passport
import authRoutes, { stripeWebhookHandler } from './routes/auth'; // Import main auth routes and separated webhook handler
import postRoutes from './routes/postRoutes'; // MongoDB post routes
// Commenting out PostgreSQL routes since we're not using PostgreSQL yet
// import postgresPostRoutes from './routes/postgresPostRoutes'; // PostgreSQL post routes
// @ts-ignore - Import the Instagram routes (JS file)
import instagramRoutes from './routes/instagramRoutes';
import queueRoutes from './routes/queueRoutes'; // Queue management routes
import googleTokenRoute from './routes/api/googleToken'; // Import Google token route

// Use default dotenv loading (looks for .env in current working directory)
dotenv.config();

// Connect to MongoDB Database (legacy)
// We'll try to connect but continue even if it fails in production
try {
  connectDB();
} catch (error) {
  console.error('Failed to connect to MongoDB:', error);
  if (process.env.NODE_ENV !== 'production') {
    process.exit(1);
  } else {
    console.warn('Running with limited functionality due to database connection failure');
  }
}

const app: Express = express();
const port = process.env.PORT || 3000; // Use PORT from .env or default to 3000 (Corrected comment)

// --- CORS Middleware ---
// Allow all origins for development. For production, restrict to your extension's origin.
// Configure CORS with specific origins
app.use(cors({
  origin: '*', // Allow all origins for development
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
})); // Configure CORS middleware

// --- Stripe Webhook Route ---
// Mount BEFORE global express.json() to ensure raw body is available for Stripe signature verification
app.post('/auth/stripe/webhook', express.raw({ type: 'application/json' }), stripeWebhookHandler);

// --- Global Middleware ---
app.use(express.json()); // For parsing application/json
app.use(express.urlencoded({ extended: true })); // For parsing application/x-www-form-urlencoded

// Passport middleware
app.use(passport.initialize());
// Note: We don't need passport.session() if using JWTs for API authentication

// Simple root route
app.get('/', (req: Request, res: Response) => {
  res.send('Social Saver Pro Backend API');
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    message: 'Notely.social API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Mount the rest of the auth routes (excluding the webhook, which is handled above)
app.use('/auth', authRoutes); // All auth routes will be prefixed with /auth

// Mount Google token route
app.use('/', googleTokenRoute); // This will handle /auth/google/token

// Mount API routes
app.use('/api/posts', postRoutes); // MongoDB post routes (legacy)
// Commenting out PostgreSQL routes since we're not using PostgreSQL yet
// app.use('/api/v2/posts', postgresPostRoutes); // PostgreSQL post routes (new)
app.use('/api/instagram', instagramRoutes); // Instagram-specific routes with S3 integration
app.use('/api/queue', queueRoutes); // Queue management and status routes

app.listen(port, () => {
  console.log(`[server]: Server is running at http://localhost:${port}`);
});