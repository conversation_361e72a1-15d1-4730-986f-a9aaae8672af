/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./popup.html",
    "./dashboard.html",
    "./settings.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Dark mode-first luxury palette
        'notely': {
          'dark': {
            'bg': '#0f0f0f',
            'surface': '#1a1a1a',
            'card': '#252525',
            'border': '#333333',
            'text': {
              'primary': '#ffffff',
              'secondary': '#b3b3b3',
              'muted': '#666666',
            }
          },
          'light': {
            'bg': '#ffffff',
            'surface': '#f8f9fa',
            'card': '#ffffff',
            'border': '#e5e7eb',
            'text': {
              'primary': '#1f2937',
              'secondary': '#6b7280',
              'muted': '#9ca3af',
            }
          },
          // Soft pastel accent colors
          'coral': '#ff6b6b',
          'mint': '#51cf66',
          'sky': '#74c0fc',
          'lavender': '#b197fc',
          'peach': '#ffa8a8',
          'sage': '#8ce99a',
        },
        // Legacy notion colors for compatibility
        'notion': {
          'bg': '#ffffff',
          'gray': {
            100: '#f7f7f7',
            200: '#e9e9e9',
            300: '#d3d3d3',
            400: '#b3b3b3',
            500: '#808080',
            600: '#666666',
            700: '#4d4d4d',
            800: '#333333',
            900: '#1a1a1a',
          },
          'border': 'rgba(0, 0, 0, 0.1)',
          'divider': 'rgba(0, 0, 0, 0.2)',
        },
      },
      fontFamily: {
        'heading': ['Eudoxus Sans', 'system-ui', 'sans-serif'],
        'body': ['DM Sans', 'system-ui', 'sans-serif'],
        'quote': ['Georgia', 'serif'],
        'sans': ['DM Sans', 'system-ui', 'sans-serif'], // Default fallback
      },
      fontSize: {
        'notion': {
          'body': ['14px', { lineHeight: '1.5' }],
          'heading': ['18px', { lineHeight: '1.3', fontWeight: '600' }],
          'title': ['24px', { lineHeight: '1.2', fontWeight: '700' }],
        },
      },
      spacing: {
        'notion': {
          'block': '24px',
          'container': '32px',
        },
        'notely': {
          'xs': '8px',
          'sm': '12px',
          'md': '16px',
          'lg': '24px',
          'xl': '32px',
          '2xl': '48px',
        },
      },
      borderRadius: {
        'notion': '3px',
        'notely': {
          'sm': '8px',
          'md': '12px',
          'lg': '16px',
          'xl': '20px',
          '2xl': '24px',
        },
      },
      borderWidth: {
        'notion': '1px',
      },
    },
  },
  plugins: [],
  future: {
    hoverOnlyWhenSupported: true,
  },
  safelist: [
    'notion-container',
    'notion-block',
    'notion-heading',
    'notion-title',
    'notion-divider',
    'notion-button',
    'notion-input',
    'notion-card',
    'text-notion-gray-400',
    'text-notion-gray-500',
    'text-notion-gray-600',
    'text-notion-gray-700',
    'bg-notion-bg',
    'bg-notion-gray-100',
    'bg-notion-gray-200',
    'bg-notion-gray-300',
    'border-notion-border',
    'border-notion-divider',
    'rounded-notion',
    'mb-notion-block',
    'py-notion-block',
    'px-notion-block',
    'w-12', 'h-7', 'w-5', 'h-5', 'translate-x-5',
  ],
};