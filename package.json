{"name": "notely", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "clean": "rm -rf dist temp", "build": "bash scripts/build.sh", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@types/react-router-dom": "^5.3.3", "cors": "^2.8.5", "date-fns": "^4.1.0", "dom-to-image-more": "^3.5.0", "firebase-admin": "^13.3.0", "html-to-image": "^1.11.13", "idb": "^8.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "stripe": "^18.1.0"}, "devDependencies": {"@eslint/js": "^8.56.0", "@tailwindcss/postcss": "^4.1.4", "@types/chrome": "^0.0.315", "@types/html2canvas": "^0.5.35", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.14", "dotenv-cli": "^8.0.0", "esbuild": "^0.21.5", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^13.24.0", "html2canvas": "^1.4.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.0", "typescript": "^5.3.3", "typescript-eslint": "^8.32.1", "vite": "^5.0.12", "vite-plugin-static-copy": "^1.0.5"}}