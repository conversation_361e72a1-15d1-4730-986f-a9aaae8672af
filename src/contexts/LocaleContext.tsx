import React, { createContext, useEffect, useState } from 'react';
import { LocaleContextType } from './LocaleContext.types';

export const LocaleContext = createContext<LocaleContextType | undefined>(undefined);

export const LocaleProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [locale, setLocaleState] = useState('en');

  // Load saved locale from storage on mount
  useEffect(() => {
    chrome.storage.sync.get({ locale: 'en' }, (result: { locale: string }) => {
      setLocaleState(result.locale);
    });
  }, []);

  const setLocale = (newLocale: string) => {
    setLocaleState(newLocale);
    chrome.storage.sync.set({ locale: newLocale });
  };

  return (
    <LocaleContext.Provider value={{ locale, setLocale }}>
      {children}
    </LocaleContext.Provider>
  );
};
