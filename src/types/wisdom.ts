export interface WisdomQuote {
  id: string;
  text: string;
  author?: string;
  source?: string;
  categories: string[];
  tags: string[];
  createdAt: string;
  shownAt?: string;
  isFavorite?: boolean;
  relatedPostIds?: string[]; // IDs of posts that influenced this quote
  // Add any additional metadata fields as needed
}

export interface UserWisdomPreferences {
  lastShownDate?: string; // ISO date string of when the last quote was shown
  preferredCategories?: string[];
  preferredAuthors?: string[];
  // Add any user preferences for quote selection
}

export interface WisdomInsight {
  // This can be used to store insights about user's saved posts
  // that help in quote selection
  topCategories: {
    category: string;
    count: number;
  }[];
  commonTags: string[];
  lastAnalyzedAt: string;
}

// Example of how to structure the storage
// This would be stored in chrome.storage.local
export interface WisdomStorage {
  quotes: WisdomQuote[];
  preferences: UserWisdomPreferences;
  insights: WisdomInsight;
}
