import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { FiArrowLeft, FiCheckCircle, FiTwitter, FiGlobe } from 'react-icons/fi';
import { FaLinkedinIn, FaReddit<PERSON><PERSON>n, <PERSON>a<PERSON><PERSON><PERSON>ram, FaPinterest } from 'react-icons/fa';
import '../index.css';
import { getSavedPosts } from '../storage';
import { BackupService } from '../utils/backup';
import { useTranslation } from '../hooks/useTranslation';
import { useLocale } from '../contexts/useLocale';
import Toggle from '../components/Toggle';

// Utility function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// --- Storage Keys ---
const CLOUD_STORAGE_KEY = 'saveMediaToCloud';
const MINDSTREAM_TIPS_KEY = 'showMindstreamTips';
const PLATFORM_INTEGRATIONS_KEY = 'platformIntegrations';
// -------------------------------------

const languageOptions = [
  { code: 'en', name: 'English' },
  { code: 'tr', name: 'Türkçe' },
  { code: 'fr', name: 'Français' },
  { code: 'de', name: 'Deutsch' },
  { code: 'es', name: 'Español' },
];

interface PlatformStats {
  posts: number;
  mediaSize: number;
  lastUpdated?: string;
}

type StatsRecord = Record<string, PlatformStats>;

type PlatformKeys = 'X/Twitter' | 'LinkedIn' | 'Reddit' | 'Instagram' | 'pinterest' | 'Web';

// Default platform integrations - all enabled by default
const defaultPlatformIntegrations: Record<PlatformKeys, boolean> = {
  'X/Twitter': true,
  'LinkedIn': true,
  'Reddit': true,
  'Instagram': true,
  'pinterest': true,
  'Web': true
};

// Platform display names and icons
const platformInfo: Record<PlatformKeys, { name: string; icon: React.ComponentType; color: string }> = {
  'X/Twitter': { name: 'X / Twitter', icon: FiTwitter, color: 'text-black' },
  'LinkedIn': { name: 'LinkedIn', icon: FaLinkedinIn, color: 'text-blue-600' },
  'Reddit': { name: 'Reddit', icon: FaRedditAlien, color: 'text-orange-500' },
  'Instagram': { name: 'Instagram', icon: FaInstagram, color: 'text-purple-500' },
  'pinterest': { name: 'Pinterest', icon: FaPinterest, color: 'text-red-600' },
  'Web': { name: 'Web Articles', icon: FiGlobe, color: 'text-green-500' }
};

export function Settings() {
  // State management
  const [stats, setStats] = useState<StatsRecord>({});
  const [loading, setLoading] = useState(true);
  const [backupStatus, setBackupStatus] = useState<string>('');
  const [saveMediaToCloud, setSaveMediaToCloud] = useState<boolean>(true);
  const [showMindstreamTips, setShowMindstreamTips] = useState<boolean>(true);
  const [platformIntegrations, setPlatformIntegrations] = useState<Record<PlatformKeys, boolean>>(defaultPlatformIntegrations);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveStatus, setSaveStatus] = useState<{show: boolean; message: string; isError: boolean}>({
    show: false, 
    message: '', 
    isError: false
  });
  
  // Hooks
  const { t } = useTranslation();
  const { locale, setLocale } = useLocale();
  
  // Services
  const backup = useMemo(() => new BackupService(), []);

  // Show save status and auto-hide after 3 seconds
  useEffect(() => {
    if (saveStatus.show) {
      const timer = setTimeout(() => {
        setSaveStatus(prev => ({...prev, show: false}));
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [saveStatus.show]);
  
  // Visual indicator for saving state
  useEffect(() => {
    if (isSaving) {
      document.body.style.cursor = 'wait';
    } else {
      document.body.style.cursor = 'default';
    }
    return () => {
      document.body.style.cursor = 'default';
    };
  }, [isSaving]);
  
  // Save settings to storage
  const saveSettings = useCallback(async () => {
    try {
      setIsSaving(true);
      console.log('[Settings] Saving settings:', {
        saveMediaToCloud,
        showMindstreamTips,
        platformIntegrations,
        locale
      });

      // Save to local storage
      await chrome.storage.local.set({
        [CLOUD_STORAGE_KEY]: saveMediaToCloud,
        [MINDSTREAM_TIPS_KEY]: showMindstreamTips,
        [PLATFORM_INTEGRATIONS_KEY]: platformIntegrations
      });

      // Save locale to sync storage to ensure it persists across devices
      await chrome.storage.sync.set({ locale });

      console.log('[Settings] Settings saved successfully');

      setSaveStatus({
        show: true,
        message: t('common.settingsSaved') || 'Settings saved successfully',
        isError: false
      });
    } catch (e) {
      console.error('[Settings] Error saving settings:', e);
      setSaveStatus({
        show: true,
        message: t('error.generic') || 'Failed to save settings',
        isError: true
      });
    } finally {
      setIsSaving(false);
    }
  }, [saveMediaToCloud, showMindstreamTips, platformIntegrations, locale, t]);

  // Load settings from storage
  const loadSettings = useCallback(async () => {
    setLoading(true);
    try {
      console.log('[Settings] Loading settings from storage...');

      // Load all local storage settings at once for efficiency
      const localStorageSettings = await new Promise<{[key: string]: unknown}>((resolve) => {
        chrome.storage.local.get([
          CLOUD_STORAGE_KEY,
          MINDSTREAM_TIPS_KEY,
          PLATFORM_INTEGRATIONS_KEY
        ], (result) => {
          console.log('[Settings] Loaded local storage settings:', result);
          resolve(result);
        });
      });

      // Apply cloud storage setting
      if (localStorageSettings[CLOUD_STORAGE_KEY] !== undefined) {
        const cloudStorageValue = Boolean(localStorageSettings[CLOUD_STORAGE_KEY]);
        console.log('[Settings] Setting cloud storage to:', cloudStorageValue);
        setSaveMediaToCloud(cloudStorageValue);
      }

      // Apply mindstream tips setting
      if (localStorageSettings[MINDSTREAM_TIPS_KEY] !== undefined) {
        const mindstreamTipsValue = Boolean(localStorageSettings[MINDSTREAM_TIPS_KEY]);
        console.log('[Settings] Setting mindstream tips to:', mindstreamTipsValue);
        setShowMindstreamTips(mindstreamTipsValue);
      }

      // Apply platform integrations settings
      if (localStorageSettings[PLATFORM_INTEGRATIONS_KEY] &&
          typeof localStorageSettings[PLATFORM_INTEGRATIONS_KEY] === 'object' &&
          localStorageSettings[PLATFORM_INTEGRATIONS_KEY] !== null) {
        // Type assertion after validation
        const savedIntegrations = localStorageSettings[PLATFORM_INTEGRATIONS_KEY] as Record<PlatformKeys, boolean>;
        const finalIntegrations = {
          ...defaultPlatformIntegrations,
          ...savedIntegrations
        };
        console.log('[Settings] Setting platform integrations to:', finalIntegrations);
        setPlatformIntegrations(finalIntegrations);
      } else {
        console.log('[Settings] No saved platform integrations found, using defaults');
        setPlatformIntegrations(defaultPlatformIntegrations);
      }

      // Load locale from sync storage to ensure it's consistent across devices
      await new Promise<void>((resolve) => {
        chrome.storage.sync.get({ locale: 'en' }, (result) => {
          console.log('[Settings] Loaded locale from sync storage:', result.locale);
          if (result.locale && typeof result.locale === 'string') {
            setLocale(result.locale);
          }
          resolve();
        });
      });
      
      // Load stats
      const posts = await getSavedPosts();
      const platformStats: StatsRecord = {};
      
      // Group posts by platform
      posts.forEach(post => {
        const platform = post.platform || 'Unknown';
        
        if (!platformStats[platform]) {
          platformStats[platform] = {
            posts: 0,
            mediaSize: 0,
            lastUpdated: undefined
          };
        }
        
        platformStats[platform].posts += 1;
        
        // Calculate media size if available
        if (post.mediaItems && Array.isArray(post.mediaItems)) {
          platformStats[platform].mediaSize += post.mediaItems.reduce((sum, item) => {
            return sum + (item.size || 0);
          }, 0);
        }
        
        // Update last updated timestamp
        const timestamp = post.timestamp || post.savedAt;
        if (timestamp) {
          if (!platformStats[platform].lastUpdated || new Date(timestamp) > new Date(platformStats[platform].lastUpdated)) {
            platformStats[platform].lastUpdated = timestamp;
          }
        }
      });
      
      setStats(platformStats);
    } catch (err) {
      console.error('Error loading settings:', err);
    } finally {
      setLoading(false);
    }
  }, [setLocale]);

  // Load settings on mount
  useEffect(() => {
    console.log('[Settings] Component mounted, loading settings...');
    loadSettings();
  }, [loadSettings]);

  // Debug: Log state changes
  useEffect(() => {
    console.log('[Settings] State updated:', {
      locale,
      saveMediaToCloud,
      showMindstreamTips,
      platformIntegrations,
      loading,
      isSaving
    });
  }, [locale, saveMediaToCloud, showMindstreamTips, platformIntegrations, loading, isSaving]);

  // Handle backup creation
  const handleBackup = async () => {
    try {
      await backup.createBackup();
      setBackupStatus('Backup created successfully!');
    } catch (error) {
      console.error('Backup error:', error);
      setBackupStatus('Failed to create backup. Please try again.');
    }
  };

  // Handle backup restore
  const handleRestore = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;
    
    try {
      setBackupStatus('Restoring backup...');
      await backup.restoreBackup(e.target.files[0]);
      setBackupStatus('Backup restored successfully!');
      loadSettings(); // Reload settings after restore
    } catch (error) {
      console.error('Restore error:', error);
      setBackupStatus('Failed to restore backup. Please check the file and try again.');
    }
  };

  // Handle clear data
  const handleClearData = async () => {
    if (window.confirm(t('settings.clearDataConfirm'))) {
      try {
        await chrome.storage.local.clear();
        setBackupStatus('All data cleared successfully!');
        loadSettings(); // Reload settings after clear
      } catch (error) {
        console.error('Clear data error:', error);
        setBackupStatus('Failed to clear data. Please try again.');
      }
    }
  };

  // Handle language change
  const handleLanguageChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLocale = e.target.value;
    console.log('[Settings] Language change requested:', newLocale);

    try {
      // Update locale in context first
      setLocale(newLocale);

      // Clear translation cache to ensure new language is applied immediately
      if (typeof window !== 'undefined' && (window as any).clearTranslationCache) {
        (window as any).clearTranslationCache();
      }

      // Save locale directly to storage
      await chrome.storage.sync.set({ locale: newLocale });

      // Also save other settings to local storage
      await chrome.storage.local.set({
        [CLOUD_STORAGE_KEY]: saveMediaToCloud,
        [MINDSTREAM_TIPS_KEY]: showMindstreamTips,
        [PLATFORM_INTEGRATIONS_KEY]: platformIntegrations
      });

      console.log('[Settings] Language changed successfully to:', newLocale);

      setSaveStatus({
        show: true,
        message: 'Language changed successfully. Refreshing page...',
        isError: false
      });

      // Force a page refresh to ensure all components use the new language
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (error) {
      console.error('[Settings] Error changing language:', error);
      setSaveStatus({
        show: true,
        message: 'Failed to change language. Please try again.',
        isError: true
      });
    }
  };

  // Handle back button click
  const handleBack = () => {
    window.location.href = '/dashboard.html';
  };

  // Save status notification component
  const SaveStatus = () => {
    if (!saveStatus.show) return null;
    
    return (
      <div className={`fixed bottom-4 right-4 p-3 rounded-md shadow-lg flex items-center ${
        saveStatus.isError ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
      }`}>
        <FiCheckCircle className="mr-2" />
        <span>{saveStatus.message}</span>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-notely-dark-bg text-notely-dark-text-primary">
      <div className="max-w-6xl mx-auto p-6">
        <div className="flex items-center mb-8">
          <button 
            onClick={handleBack}
            className="mr-4 p-2 rounded-full hover:bg-notely-dark-surface transition-colors"
            aria-label="Back"
          >
            <FiArrowLeft className="w-5 h-5" />
          </button>
          <h1 className="text-2xl font-bold font-georgia">{t('settings.title')}</h1>
        </div>
        
        {/* Main content with two-column layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left Column */}
          <div className="space-y-6">
            {/* Language Selector */}
            <div className="p-6 bg-notely-dark-card rounded-lg shadow-sm border border-notely-dark-border">
              <h2 className="text-lg font-semibold text-notely-dark-text-primary mb-4">{t('settings.language')}</h2>
              <select
                value={locale}
                onChange={handleLanguageChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base bg-notely-dark-surface border-notely-dark-border text-notely-dark-text-primary focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                aria-label={t('settings.language')}
              >
                {languageOptions.map((option) => (
                  <option key={option.code} value={option.code}>
                    {option.name}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Cloud Storage Toggle */}
            <div className="p-6 bg-notely-dark-card rounded-lg shadow-sm border border-notely-dark-border">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-lg font-semibold text-notely-dark-text-primary mb-2">{t('settings.cloudStorage')}</h2>
                  <p className="text-sm text-notely-dark-text-secondary">
                    {t('settings.cloudStorageDescription')}
                  </p>
                </div>
                <div className="flex items-center">
                  <Toggle
                    enabled={saveMediaToCloud}
                    onChange={async (enabled) => {
                      console.log('[Settings] Cloud storage toggle:', enabled);
                      setSaveMediaToCloud(enabled);

                      // Save to storage immediately
                      try {
                        await chrome.storage.local.set({
                          [CLOUD_STORAGE_KEY]: enabled,
                          [MINDSTREAM_TIPS_KEY]: showMindstreamTips,
                          [PLATFORM_INTEGRATIONS_KEY]: platformIntegrations
                        });

                        console.log('[Settings] Cloud storage setting saved successfully');

                        setSaveStatus({
                          show: true,
                          message: t('common.settingsSaved') || 'Settings saved successfully',
                          isError: false
                        });
                      } catch (error) {
                        console.error('[Settings] Error saving cloud storage setting:', error);
                        setSaveStatus({
                          show: true,
                          message: 'Failed to save cloud storage setting. Please try again.',
                          isError: true
                        });
                        // Revert state on error
                        setSaveMediaToCloud(!enabled);
                      }
                    }}
                  />
                </div>
              </div>
            </div>
            
            {/* Mindstream Tips Toggle */}
            <div className="p-6 bg-notely-dark-card rounded-lg shadow-sm border border-notely-dark-border">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-lg font-semibold text-notely-dark-text-primary mb-2">{t('settings.mindstreamTips')}</h2>
                  <p className="text-sm text-notely-dark-text-secondary">
                    {t('settings.mindstreamTipsDescription')}
                  </p>
                </div>
                <div className="flex items-center">
                  <Toggle
                    enabled={showMindstreamTips}
                    onChange={async (enabled) => {
                      console.log('[Settings] Mindstream tips toggle:', enabled);
                      setShowMindstreamTips(enabled);

                      // Save to storage immediately
                      try {
                        await chrome.storage.local.set({
                          [CLOUD_STORAGE_KEY]: saveMediaToCloud,
                          [MINDSTREAM_TIPS_KEY]: enabled,
                          [PLATFORM_INTEGRATIONS_KEY]: platformIntegrations
                        });

                        console.log('[Settings] Mindstream tips setting saved successfully');

                        setSaveStatus({
                          show: true,
                          message: t('common.settingsSaved') || 'Settings saved successfully',
                          isError: false
                        });
                      } catch (error) {
                        console.error('[Settings] Error saving mindstream tips setting:', error);
                        setSaveStatus({
                          show: true,
                          message: 'Failed to save mindstream tips setting. Please try again.',
                          isError: true
                        });
                        // Revert state on error
                        setShowMindstreamTips(!enabled);
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
          
          {/* Right Column */}
          <div className="space-y-6">
            {/* Platform Integrations Section */}
            <div className="p-6 bg-notely-dark-card rounded-lg shadow-sm border border-notely-dark-border">
              <h2 className="text-lg font-semibold text-notely-dark-text-primary mb-3">{t('settings.platformIntegrations')}</h2>
              <p className="text-sm text-notely-dark-text-secondary mb-4">{t('settings.platformIntegrationsDescription')}</p>
              
              {/* Platform Toggle Switches */}
              <div className="space-y-4 mt-6">
                {(Object.keys(defaultPlatformIntegrations) as PlatformKeys[]).map((platform) => (
                  <div key={platform} className="flex justify-between items-center py-3 border-b border-notely-dark-border last:border-0">
                    <div className="flex items-center">
                      <div className={`mr-3 ${platformInfo[platform].color}`}>
                        {React.createElement(platformInfo[platform].icon, { className: 'w-5 h-5' })}
                      </div>
                      <span className="text-sm font-medium text-notely-dark-text-primary">{platformInfo[platform].name}</span>
                    </div>
                    <Toggle
                      enabled={platformIntegrations[platform]}
                      onChange={async (enabled) => {
                        console.log(`[Settings] Platform toggle ${platform}:`, enabled);

                        // Update state immediately
                        const newIntegrations = {
                          ...platformIntegrations,
                          [platform]: enabled
                        };

                        console.log('[Settings] Updated platform integrations:', newIntegrations);
                        setPlatformIntegrations(newIntegrations);

                        // Save to storage immediately with the new state
                        try {
                          await chrome.storage.local.set({
                            [CLOUD_STORAGE_KEY]: saveMediaToCloud,
                            [MINDSTREAM_TIPS_KEY]: showMindstreamTips,
                            [PLATFORM_INTEGRATIONS_KEY]: newIntegrations
                          });

                          console.log(`[Settings] Platform ${platform} ${enabled ? 'enabled' : 'disabled'} successfully`);

                          setSaveStatus({
                            show: true,
                            message: t('common.settingsSaved') || 'Settings saved successfully',
                            isError: false
                          });
                        } catch (error) {
                          console.error(`[Settings] Error saving platform ${platform} setting:`, error);
                          setSaveStatus({
                            show: true,
                            message: `Failed to save ${platform} setting. Please try again.`,
                            isError: true
                          });

                          // Revert the state change on error
                          setPlatformIntegrations(platformIntegrations);
                        }
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Storage Stats */}
            {!loading && Object.keys(stats).length > 0 && (
              <div className="p-6 bg-notely-dark-card rounded-lg shadow-sm border border-notely-dark-border">
                <h2 className="text-lg font-semibold text-notely-dark-text-primary mb-4">{t('settings.storageStats')}</h2>
                <div className="space-y-4">
                  {Object.entries(stats).map(([platform, stat]) => (
                    <div key={platform} className="border border-notely-dark-border rounded-md p-4 hover:bg-notely-dark-surface transition-colors">
                      <div className="flex justify-between">
                        <span className="font-medium capitalize text-notely-dark-text-primary">{platform}</span>
                        <span className="text-sm text-notely-dark-text-secondary font-medium">
                          {stat.posts} {t('settings.posts')}
                        </span>
                      </div>
                      <div className="mt-2">
                        <span className="text-sm text-notely-dark-text-secondary">
                          {t('settings.mediaSize')}: {formatFileSize(stat.mediaSize || 0)}
                        </span>
                        {stat.lastUpdated && (
                          <span className="block text-xs text-notely-dark-text-muted mt-1">
                            {t('settings.lastUpdated')}: {new Date(stat.lastUpdated).toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Backup & Restore */}
            <div className="p-6 bg-notely-dark-card rounded-lg shadow-sm border border-notely-dark-border">
              <h2 className="text-lg font-semibold text-notely-dark-text-primary mb-4">{t('settings.backupRestore')}</h2>
              <div className="space-y-6">
                <div className="bg-blue-900/20 p-4 rounded-lg border border-blue-800/30">
                  <h3 className="font-medium text-blue-400 mb-2">{t('settings.createBackup')}</h3>
                  <p className="mb-3 text-sm text-blue-300">{t('settings.backupDescription')}</p>
                  <button
                    onClick={handleBackup}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                  >
                    {t('settings.createBackup')}
                  </button>
                </div>
                
                <div className="bg-notely-dark-surface p-4 rounded-lg border border-notely-dark-border">
                  <h3 className="font-medium text-notely-dark-text-primary mb-2">{t('settings.restoreBackup')}</h3>
                  <p className="mb-3 text-sm text-notely-dark-text-secondary">{t('settings.restoreDescription')}</p>
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleRestore}
                    className="block w-full text-sm text-notely-dark-text-secondary file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-900/30 file:text-blue-300 hover:file:bg-blue-800/30 cursor-pointer"
                    aria-label={t('settings.restoreBackup')}
                  />
                </div>
                
                {backupStatus && (
                  <div className={`p-3 rounded-md border ${backupStatus.includes('success') ? 'bg-green-900/20 text-green-400 border-green-800/30' : 'bg-red-900/20 text-red-400 border-red-800/30'}`}>
                    <p className="text-sm font-medium">{backupStatus}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Clear Data */}
            <div className="p-6 bg-notely-dark-card rounded-lg shadow-sm border border-red-900/30">
              <h2 className="text-lg font-semibold text-red-400 mb-4">{t('settings.dangerZone')}</h2>
              <div className="bg-red-900/20 p-4 rounded-lg border border-red-800/30">
                <h3 className="font-medium text-red-300 mb-2">{t('settings.clearAllData')}</h3>
                <p className="text-sm text-red-400 mb-4">
                  {t('settings.clearAllDataWarning')}
                </p>
                <button
                  onClick={handleClearData}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 text-sm transition-colors"
                >
                  {t('settings.clearAllDataButton')}
                </button>
              </div>
            </div>
          </div>
        </div>
        <SaveStatus />
      </div>
    </div>
  );
}

// SaveStatus component is defined at the top level of the file
// to avoid any potential issues with hooks
