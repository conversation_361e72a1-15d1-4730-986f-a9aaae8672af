import React from 'react';
import { createRoot } from 'react-dom/client';
import { Settings } from './settings';
import { LocaleProvider } from '../contexts/LocaleContext';
import '../index.css';

const container = document.getElementById('root');
if (container) {
  const root = createRoot(container);
  root.render(
    <React.StrictMode>
      <LocaleProvider>
        <Settings />
      </LocaleProvider>
    </React.StrictMode>
  );
}
