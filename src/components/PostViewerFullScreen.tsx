import React, { useState, useEffect, useRef, useCallback } from 'react';
import { getUserPlan } from '../services/authService';
import ProxyImage from './ProxyImage';
import { PostContentRenderer } from './PostContentRenderer';
import MultiItemInput from './MultiItemInput';
// Import formatForDisplay for proper tag and category formatting
import { formatForDisplay, formatForStorage } from '../utils/formatUtils';
import { updatePostDetails, getAllCategories, getAllTags, saveAllCategories, saveAllTags } from '../storage';
import { useAIEnrichment } from '../hooks/useAIEnrichment';
import { useTranslation } from '../hooks/useTranslation';

// --- Icon Stubs (replace with actual icons or imports) ---
const IconX: React.FC<{ className?: string }> = ({ className = "w-6 h-6" }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
  </svg>
);
const IconClipboard: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth={1.5}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184" />
  </svg>
);
// Removed unused IconPlus component

const IconSave: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
  </svg>
);

// Removed unused IconSparkles component

const IconRefresh: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth={1.5}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
  </svg>
);



// --- Platform Specific SVG Icons ---
const XLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path>
  </svg>
);

const LinkedInLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M20.5 2h-17A1.5 1.5 0 002 3.5v17A1.5 1.5 0 003.5 22h17a1.5 1.5 0 001.5-1.5v-17A1.5 1.5 0 0020.5 2zM8 19H5v-9h3zM6.5 8.25A1.75 1.75 0 118.25 6.5 1.75 1.75 0 016.5 8.25zM19 19h-3v-4.74c0-1.42-.6-1.93-1.38-1.93-.78 0-1.22.52-1.42 1.02-.08.18-.09.42-.09.66V19h-3V10h3v1.32c.41-.64 1.14-1.32 2.67-1.32 1.98 0 3.23 1.29 3.23 4.01z"></path>
  </svg>
);

const RedditLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm1-12.78c.1-.01.2-.01.31-.01.39 0 .76.07 1.11.2.33.12.63.3.89.53.26.23.47.51.63.81.16.31.25.65.25.99 0 .31-.07.6-.2.88-.13.27-.32.5-.56.68-.24.18-.52.31-.83.39-.31.08-.65.12-1 .12h-.62v2.11c0 .21-.07.38-.21.53s-.32.22-.53.22c-.21 0-.38-.07-.53-.22s-.22-.32-.22-.53v-2.11H12c-.38 0-.72-.05-1.03-.14s-.58-.24-.81-.43c-.23-.19-.4-.43-.51-.71s-.17-.59-.17-.93c0-.35.07-.68.2-.99s.31-.58.55-.82c.24-.24.53-.42.86-.55.33-.13.7-.19 1.1-.19zm-.13 2.74c-.16 0-.3.02-.41.05s-.21.08-.29.15c-.08.07-.15.15-.19.25s-.07.21-.07.33c0 .19.06.35.17.48s.26.2.45.2h.41v-.75c0-.1-.01-.19-.02-.26s-.04-.13-.07-.18c-.03-.05-.07-.09-.12-.11s-.1-.04-.17-.04zm.29-1.25c-.22 0-.42.03-.58.1s-.29.16-.4.29c-.11.12-.19.27-.24.44s-.07.34-.07.53c0 .24.04.46.12.65s.2.36.35.49c.15.13.32.23.52.31.2.07.42.11.66.11.22 0 .42-.03.58-.1s.29-.16.4-.29c.11-.12.19-.27.24-.44s-.07-.34-.07-.53c0-.24-.04-.46-.12-.65s-.2-.36-.35-.49c-.15-.13-.32-.23-.52-.31-.2-.07-.42-.11-.66-.11z"></path>
  </svg>
);

const InstagramLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 1.268.058 2.14.29 2.91.588.78.306 1.455.78 2.13 1.455.67.67 1.148 1.35 1.455 2.13.298.77.53 1.643.588 2.912.058 1.265.07 1.646.07 4.85s-.012 3.584-.07 4.85c-.058 1.27-.29 2.14-.588 2.91-.306.78-.78 1.455-1.455 2.13-.67.67-1.35 1.148-2.13 1.455-.77.298-1.642.53-2.91.588-1.265.058-1.646.07-4.85.07s-3.584-.012-4.85-.07c-1.268-.058-2.14-.29-2.91-.588-.78-.306-1.455-.78-2.13-1.455-.67-.67-1.148-1.35-1.455-2.13-.298-.77-.53-1.643-.588-2.912-.058-1.265-.07-1.646-.07-4.85s.012-3.584.07-4.85c.058-1.27.29-2.14.588-2.91.306-.78.78-1.455 1.455-2.13.67-.67 1.35-1.148 2.13-1.455.77-.298 1.642-.53 2.91.588C8.416 2.175 8.796 2.163 12 2.163zm0 1.802C8.84 3.965 8.508 3.977 7.302 4.03c-1.14.053-1.833.266-2.37.476-.557.22-.978.5-1.376.9-.4.4-.68.82-.9 1.377-.21.536-.423 1.23-.475 2.37C2.467 8.507 2.456 8.84 2.456 12s.011 3.493.067 4.698c.052 1.14.265 1.833.475 2.37.22.556.5.978.9 1.376.4.4.818.68 1.375.9.536.21 1.23.423 2.37.475 1.207.055 1.539.067 4.698.067s3.491-.012 4.698-.067c1.14-.052 1.832-.265 2.37-.475.556-.22.978-.5 1.376-.9.4-.4.68-.818.9-1.375.21-.536.423-1.23.475-2.37.054-1.206.067-1.539.067-4.698s-.013-3.493-.067-4.698c-.052-1.14-.265-1.833-.475-2.37-.22-.557-.5-.978-.9-1.376-.4-.4-.82-.68-1.377-.9-.536-.21-1.23-.423-2.37-.475C15.493 3.977 15.16 3.965 12 3.965zm0 3.27c-2.415 0-4.366 1.95-4.366 4.366s1.95 4.366 4.366 4.366 4.366-1.95 4.366-4.366S14.415 7.232 12 7.232zm0 7.032c-1.473 0-2.666-1.193-2.666-2.666s1.193-2.666 2.666-2.666 2.666 1.193 2.666 2.666-1.193 2.666-2.666 2.666zm4.965-7.167c0 .557-.45.01-1.007.01s-1.008-.453-1.008-1.01c0-.556.45-1.007 1.008-1.007s1.007.45 1.007 1.007z"></path>
  </svg>
);

const FacebookLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
 <svg viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12c0 4.84 3.44 8.87 8 9.8V15H8v-3h2V9.5C10 7.57 11.57 6 13.5 6H16v3h-1.7c-.64 0-1.3.27-1.3.7V12h3l-.5 3h-2.5v6.8c4.56-.93 8-4.96 8-9.8z"></path>
  </svg>
);

const PinterestLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M12 2C6.48 2 2 6.48 2 12c0 4.08 2.55 7.59 6.08 9.13.03-.24.05-.6.05-.85 0-.61-.02-1.22-.05-1.72-.03-.52-.19-1.03-.19-1.03s-.38.05-.38 1.44c0 .83.51 1.79 1.16 1.79.82 0 1.21-.98 1.21-2.15 0-1.59-.97-2.79-2.19-2.79-.85 0-1.47.64-1.47 1.47 0 .5.17.87.38 1.15.05.07.06.13.04.22-.03.1-.08.38-.11.49-.04.13-.07.17-.18.12-.6-.27-1.04-1.05-1.04-2.14 0-1.69 1.34-3.67 3.86-3.67 2.01 0 3.55 1.5 3.55 3.36 0 .9-.23 1.89-.61 2.71-.2.46-.13.96.07 1.4.06.12.18.25.28.25.36 0 .5-.42.5-.84 0-.75-.28-1.4-.28-2.11 0-1.28.76-2.4 2.12-2.4 1.17 0 2.03.87 2.03 2.21 0 1.47-.56 2.92-1.36 2.92-.29 0-.58-.26-.58-.56 0-.35.12-.62.12-.62s.33-1.36.33-2.1c0-.91-.36-1.59-1.11-1.59-.71 0-1.28.69-1.28 1.51 0 .6.14.95.14 1.39 0 .46-.17.99-.17 1.31 0 .22.01.31.01.31s-.64 2.71-.75 3.16c-.19.76-.04 1.64.12 2.35.08.36.01.73-.21.95-.12.12-.28.18-.45.18-.19 0-.38-.08-.52-.25-.98-1.15-1.3-2.83-1.3-4.69 0-.99.11-1.92.32-2.78.01-.03.01-.07 0-.1z"></path>
  </svg>
);

// --- PlatformLogo Stub (replace with your actual PlatformLogo component) ---
type Platform = 'instagram' | 'x' | 'reddit' | 'linkedin' | 'pinterest' | 'X/Twitter' | 'facebook' | string; // Extend as needed

// Updated PlatformLogo component using SVGs
const PlatformLogo: React.FC<{ platform: Platform; className?: string }> = ({ platform, className = "w-6 h-6" }) => {
  const platformString = String(platform).toLowerCase();

  if (platformString.includes('x') || platformString.includes('twitter')) {
    return <XLogo className={className} />;
  }
  if (platformString.includes('linkedin')) {
    return <LinkedInLogo className={className} />;
  }
  if (platformString.includes('reddit')) {
    return <RedditLogo className={className} />;
  }
  if (platformString.includes('instagram')) {
    return <InstagramLogo className={className} />;
  }
  if (platformString.includes('facebook')) {
    return <FacebookLogo className={className} />;
  }
  if (platformString.includes('pinterest')) {
    return <PinterestLogo className={className} />;
  }

  // Fallback for unknown platforms
  const logoChar = platformString.charAt(0).toUpperCase() || 'P';
  return (
    <div className={`flex items-center justify-center text-xs font-semibold rounded-sm bg-notely-surface text-notely-text-secondary ${className}`} title={String(platform)}>
      {logoChar}
    </div>
  );
};

// --- Data Types based on user's JSON schema ---
interface AIInSightData {
  sentiment: 'positive' | 'neutral' | 'negative';
  emoji: string;
  contextTags: string[];
}

export interface PostWithAIData {
  id: string;
  platform: Platform;
  mediaType: 'image' | 'video' | 'text';
  mediaUrl?: string;
  text?: string; // Content for text posts, or caption for media posts
  content?: string; // Alternative content field
  author: string;
  timestamp: string; // ISO
  stats?: { likes?: number; comments?: number; shares?: number };
  snapNote?: string | null; // AI-generated note
  notes?: string | null; // Personal user notes
  inSight?: AIInSightData | null;
  fastTake?: string | null;
  tags?: string[] | null;
  categories?: string[] | null;
  // Thread-specific fields
  isThread?: boolean;
  threadId?: string;
  threadPosition?: number;
  threadLength?: number;
}

// --- Component Props ---
interface PostViewerFullScreenProps {
  post: PostWithAIData | null;
  onClose: () => void;
  onAddCategory: (postId: string, category: string) => void;
  onAddTag?: (postId: string, tag: string) => void;
  onRemoveTag?: (postId: string, tag: string) => void;
  onRemoveCategory?: (postId: string, category: string) => void; // Added for consistency if categories are removable
  onUpdateNotes?: (postId: string, notes: string) => void; // Added for notes functionality
  onUpdateCategories?: (postId: string, categories: string[]) => void; // Added for bulk category updates
  onUpdateTags?: (postId: string, tags: string[]) => void; // Added for bulk tag updates
  // Thread-specific props
  threadPosts?: PostWithAIData[]; // All posts in the thread (if this is a thread post)
  currentThreadIndex?: number; // Current post index in the thread
  onThreadNavigate?: (index: number) => void; // Navigate to different post in thread
}

// --- Chip Component ---
interface ChipProps {
  text: string;
  onRemove?: () => void;
  color?: string;
  className?: string;
  interactive?: boolean;
}

const Chip: React.FC<ChipProps> = ({ text, onRemove, color = 'bg-notely-surface text-notely-text-secondary', className = '', interactive = true }) => {
  // We can't use the hook here since this is a standalone component, so we'll keep the English text
  // In a real implementation, you'd want to pass the translation function as a prop or use a context
  return (
    <div
      className={`px-2.5 py-1 text-xs font-medium rounded-full flex items-center ${color} ${className} ${interactive && onRemove ? 'cursor-pointer hover:opacity-80' : 'cursor-default'}`}
      onClick={interactive && onRemove ? (e) => { e.stopPropagation(); onRemove(); } : undefined}
      title={interactive && onRemove ? `Remove ${text}` : text}
    >
      {text}
      {interactive && onRemove && (
        <button
          type="button"
          className="ml-1 -mr-0.5 p-0.5 rounded-full hover:bg-notely-text-primary/10 focus:outline-none"
          aria-label={`Remove ${text}`}
        >
          <IconX className="w-3 h-3" />
        </button>
      )}
    </div>
  );
};

// --- Helper Functions ---
const formatTimestamp = (isoString: string): string => {
  if (!isoString) return "Unknown date";
  try {
    const date = new Date(isoString);
    if (isNaN(date.getTime())) return "Invalid date";
    const now = new Date();
    const diffInSeconds = Math.round((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds}s ago`;
    const diffInMinutes = Math.round(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const diffInHours = Math.round(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.round(diffInHours / 24);
    if (diffInDays === 1) return `Yesterday`;
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  } catch (e) {
    console.error("Error formatting timestamp:", e);
    return "Date error";
  }
};

const formatStatNumber = (num?: number): string => {
  if (num === undefined || num === null) return '0';
  if (num < 1000) return num.toString();
  if (num < 1000000) return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
  return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
};

// --- Helper Functions ---
const isBase64Image = (url: string): boolean => {
  return url?.startsWith('data:image/');
};

// --- Main Component ---
const PostViewerFullScreen: React.FC<PostViewerFullScreenProps> = ({
  post,
  onClose,
  // onAddCategory, // Unused prop
  onAddTag,
  // onRemoveTag, // Unused prop
  // onRemoveCategory, // Unused prop
  onUpdateNotes,
  onUpdateCategories,
  onUpdateTags,
  threadPosts,
  currentThreadIndex = 0,
  onThreadNavigate,
}) => {
  const [localPost, setLocalPost] = useState<PostWithAIData | null>(post);
  const [notes, setNotes] = useState<string>('');
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveNotification, setSaveNotification] = useState<string>('');
  const [allCategories, setAllCategories] = useState<string[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);
  // Removed unused activeTab state
  const [userPlan, setUserPlan] = useState<{plan?: string}>({});
  // Removed unused usageStats state
  const [copyNotification, setCopyNotification] = useState<string>('');
  const [isAnimating, setIsAnimating] = useState<boolean>(true);
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const modalRef = useRef<HTMLDivElement>(null);

  // Translation hook
  const { t } = useTranslation();

  // AI Enrichment hook - using 'free' as fallback to avoid type errors
  const aiEnrichment = useAIEnrichment(userPlan?.plan === 'premium' ? 'premium' : 'free');
  const [isAIProcessing, setIsAIProcessing] = useState(false);
  const [aiError, setAIError] = useState<string | null>(null);

  // Thread navigation state
  const isThreadMode = !!(threadPosts && threadPosts.length > 1);
  const currentPost = isThreadMode ? threadPosts[currentThreadIndex] : post;
  const threadLength = threadPosts?.length || 1;

  // Thread navigation helpers
  const canNavigatePrevious = isThreadMode && currentThreadIndex > 0;
  const canNavigateNext = isThreadMode && currentThreadIndex < threadLength - 1;

  // Handle close with animation - moved up and wrapped in useCallback
  const handleClose = useCallback(() => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
    }, 300); // Match animation duration
  }, [onClose]);

  const handlePreviousPost = useCallback(() => {
    if (canNavigatePrevious && onThreadNavigate) {
      onThreadNavigate(currentThreadIndex - 1);
    }
  }, [canNavigatePrevious, onThreadNavigate, currentThreadIndex]);

  const handleNextPost = useCallback(() => {
    if (canNavigateNext && onThreadNavigate) {
      onThreadNavigate(currentThreadIndex + 1);
    }
  }, [canNavigateNext, onThreadNavigate, currentThreadIndex]);

  // Initialize user plan for usage stats display and load categories/tags
  useEffect(() => {
    const initializeData = async () => {
      try {
        const plan = await getUserPlan();
        console.log('PostViewerFullScreen: getUserPlan returned:', plan);
        // Fix type issue by creating a proper object
        setUserPlan(plan ? { plan: plan } : {});

        // Removed getUsageStats call as it's not imported or defined
        // Just load categories and tags for autocomplete
        const categories = await getAllCategories();
        const tags = await getAllTags();
        setAllCategories(categories);
        setAllTags(tags);
        console.log('PostViewerFullScreen: Loaded categories:', categories.length, 'tags:', tags.length);

        // Initialize AI enrichment after user plan is loaded
        if (plan) {
          console.log('Initializing AI enrichment for user plan:', plan);
          await aiEnrichment.checkAIAvailability();
        }
      } catch (error) {
        console.error('Error initializing data:', error);
      }
    };

    if (currentPost) {
      initializeData();
      // Initialize notes with existing personal notes (not AI snapNote)
      setNotes(currentPost.notes || '');
      // Update local post state when prop changes
      setLocalPost(currentPost);
    }
  }, [currentPost, aiEnrichment]);

  // Update local post when it changes
  useEffect(() => {
    if (currentPost) {
      setLocalPost(currentPost);
      setNotes(currentPost.notes || '');
    }
  }, [currentPost]);

  useEffect(() => {
    if (!currentPost) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleClose();
      } else if (event.key === 'ArrowLeft' && canNavigatePrevious) {
        handlePreviousPost();
      } else if (event.key === 'ArrowRight' && canNavigateNext) {
        handleNextPost();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.body.style.overflow = 'hidden'; // Prevent background scroll

    // Focus trapping
    const focusableElementsString = 'a[href]:not([disabled]), button:not([disabled]), textarea, input[type="text"]:not([disabled]), input[type="radio"]:not([disabled]), input[type="checkbox"]:not([disabled]), select:not([disabled])';
    const modal = modalRef.current;
    if (!modal) return;

    const focusableElements = Array.from(modal.querySelectorAll<HTMLElement>(focusableElementsString));
    if (focusableElements.length > 0) {
      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];
      firstElement?.focus();

      const handleTabKeyPress = (event: KeyboardEvent) => {
        if (event.key === 'Tab') {
          if (event.shiftKey) { // Shift + Tab
            if (document.activeElement === firstElement) {
              lastElement?.focus();
              event.preventDefault();
            }
          } else { // Tab
            if (document.activeElement === lastElement) {
              firstElement?.focus();
              event.preventDefault();
            }
          }
        }
      };
      modal.addEventListener('keydown', handleTabKeyPress);

      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        modal.removeEventListener('keydown', handleTabKeyPress);
        document.body.style.overflow = 'auto';
      };
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'auto';
    };

  }, [currentPost, onClose, canNavigatePrevious, canNavigateNext, handlePreviousPost, handleNextPost, handleClose]);

  // Animation effects
  useEffect(() => {
    // Start entrance animation
    setIsAnimating(true);
    const timer = setTimeout(() => {
      setIsAnimating(false);
    }, 50); // Small delay to ensure initial state is rendered

    return () => clearTimeout(timer);
  }, []);

  // handleClose function moved up and wrapped in useCallback

  const handleCopySnapNote = useCallback(() => {
    const snapNote = currentPost?.snapNote;
    if (snapNote) {
      navigator.clipboard.writeText(snapNote)
        .then(() => {
          setCopyNotification(t('postViewer.snapNoteCopied'));
          setTimeout(() => setCopyNotification(''), 2000);
        })
        .catch(err => console.error('Failed to copy SnapNote: ', err));
    }
  }, [currentPost]);

  const handleSaveNotes = useCallback(async () => {
    if (!currentPost || isSaving) return;

    setIsSaving(true);
    try {
      // Save personal notes to the 'notes' field, not 'snapNote' which is AI-generated
      await updatePostDetails(currentPost.id, { notes });

      // Update local post state immediately for UI
      setLocalPost(prevPost => prevPost ? { ...prevPost, notes } : null);

      // If onUpdateNotes callback is provided, call it
      if (onUpdateNotes) {
        onUpdateNotes(currentPost.id, notes);
      }

      setSaveNotification(t('postViewer.notesSaved'));
      setTimeout(() => setSaveNotification(''), 2000);
    } catch (error) {
      console.error('Failed to save notes:', error);
      setSaveNotification(t('postViewer.failedToSaveNotes'));
      setTimeout(() => setSaveNotification(''), 2000);
    } finally {
      setIsSaving(false);
    }
  }, [currentPost, notes, onUpdateNotes, isSaving]);

  const handleCategoryChange = async (newCategories: string[]) => {
    if (!currentPost) return;

    try {
      // Update the post with new categories
      await updatePostDetails(currentPost.id, { categories: newCategories });

      // Update master list
      const existingCategories = await getAllCategories();
      const updatedCategories = [...new Set([...existingCategories, ...newCategories])];
      await saveAllCategories(updatedCategories);
      setAllCategories(updatedCategories);

      // Update local post state immediately for UI
      // Use type assertion to handle the string[] vs expected category type incompatibility
      // This is a temporary solution until the types are properly aligned
      setLocalPost(prevPost => prevPost ? { 
        ...prevPost, 
        categories: newCategories as any // Using 'any' temporarily to bypass type checking
      } : null);

      // Update parent component state
      if (onUpdateCategories) {
        onUpdateCategories(currentPost.id, newCategories);
      }
    } catch (error) {
      console.error('Error updating categories:', error);
    }
  };

  const handleTagChange = async (newTags: string[]) => {
    if (!currentPost) return;

    try {
      // Update the post with new tags
      await updatePostDetails(currentPost.id, { tags: newTags });

      // Update master list
      const existingTags = await getAllTags();
      const updatedTags = [...new Set([...existingTags, ...newTags])];
      await saveAllTags(updatedTags);
      setAllTags(updatedTags);

      // Update local post state immediately for UI
      setLocalPost(prevPost => prevPost ? { ...prevPost, tags: newTags } : null);

      // Update parent component state
      if (onUpdateTags) {
        onUpdateTags(currentPost.id, newTags);
      }
    } catch (error) {
      console.error('Error updating tags:', error);
    }
  };

  // AI Enrichment Functions
  const handleAIEnrichment = useCallback(async (forceRegenerate = false) => {
    console.log('[AI] handleAIEnrichment called:', {
      hasPost: !!currentPost,
      isProcessing: isAIProcessing,
      forceRegenerate,
      postId: currentPost?.id
    });

    if (!currentPost || isAIProcessing) {
      console.log('[AI] Skipping enrichment - no post or already processing');
      return;
    }

    // Check if AI data already exists and we're not forcing regeneration
    if (!forceRegenerate && currentPost.snapNote && currentPost.inSight && currentPost.fastTake) {
      console.log('[AI] AI data already exists, skipping enrichment');
      return;
    }

    console.log('[AI] Starting AI enrichment process...');
    setIsAIProcessing(true);
    setAIError(null);

    try {
      // Check AI availability first
      console.log('[AI] Checking AI availability...');
      await aiEnrichment.checkAIAvailability();

      console.log('[AI] AI availability check result:', {
        canUseAI: aiEnrichment.canUseAI,
        usageInfo: aiEnrichment.usageInfo
      });

      if (!aiEnrichment.canUseAI) {
        const errorMsg = aiEnrichment.usageInfo?.reason || 'AI features not available';
        console.log('[AI] Cannot use AI:', errorMsg);
        setAIError(errorMsg);
        return;
      }

      // Prepare content for AI analysis
      const postContent = currentPost.text || currentPost.content || '';
      const imageUrl = currentPost.mediaUrl && currentPost.mediaType === 'image' ? currentPost.mediaUrl : undefined;

      console.log('[AI] Starting AI enrichment for post:', currentPost.id);
      console.log('[AI] Post content length:', postContent.length);
      console.log('[AI] Has image:', !!imageUrl);

      // Call AI enrichment
      const result = await aiEnrichment.enrichPost(postContent, imageUrl);

      console.log('[AI] AI enrichment result:', result);

      if (result) {
        // Update post with AI data
        const aiData = {
          snapNote: result.snapNote,
          inSight: result.insight,
          fastTake: result.fastTake,
          // Don't override user-set categories and tags, only add if empty
          ...((!currentPost.categories || currentPost.categories.length === 0) && result.aiCategory ? { categories: [result.aiCategory] } : {}),
          ...((!currentPost.tags || currentPost.tags.length === 0) && result.aiTags.length > 0 ? { tags: result.aiTags } : {})
        };

        console.log('[AI] Updating post with AI data:', aiData);
        // Use type assertion to handle type incompatibility with categories
        await updatePostDetails(currentPost.id, aiData as any);

        // Update local post state
        setLocalPost(prevPost => prevPost ? { ...prevPost, ...aiData } : null);

        console.log('[AI] AI enrichment completed successfully');
      } else {
        console.log('[AI] No result from AI enrichment');
      }
    } catch (error) {
      console.error('[AI] AI enrichment failed:', error);
      setAIError(error instanceof Error ? error.message : 'AI enrichment failed');
    } finally {
      setIsAIProcessing(false);
      console.log('[AI] AI enrichment process finished');
    }
  }, [currentPost, isAIProcessing, aiEnrichment]);

  // Check if AI data is missing and trigger enrichment
  const shouldTriggerAI = useCallback(() => {
    if (!currentPost) return false;
    // Allow AI for both logged in users (free/premium) and logged out users (treated as free)

    // Check if any AI fields are missing
    const missingAIData = !currentPost.snapNote || !currentPost.inSight || !currentPost.fastTake;
    console.log('[AI] Should trigger AI enrichment:', missingAIData, 'for post:', currentPost.id);

    return missingAIData;
  }, [currentPost]); // Removed userPlan as it's not used in this function

  // Auto-trigger AI enrichment when AI becomes available and post needs enrichment
  useEffect(() => {
    // Only trigger if:
    // 1. AI availability has been checked (usageInfo exists)
    // 2. AI is available for use
    // 3. Post needs AI enrichment
    // 4. Not currently processing
    if (aiEnrichment.usageInfo && aiEnrichment.canUseAI && shouldTriggerAI() && !isAIProcessing) {
      console.log('[AI] Auto-triggering AI enrichment for post:', currentPost?.id);
      handleAIEnrichment(false);
    }
  }, [aiEnrichment.usageInfo, aiEnrichment.canUseAI, shouldTriggerAI, isAIProcessing, handleAIEnrichment, currentPost?.id]);







  if (!currentPost || !localPost) return null;

  return (
    <div
      ref={modalRef}
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-all duration-300 ease-out ${
        isClosing
          ? 'bg-black/0 backdrop-blur-none'
          : isAnimating
            ? 'bg-black/0 backdrop-blur-none'
            : 'bg-black/70 backdrop-blur-sm'
      }`}
      onClick={handleClose} // Close on overlay click
      aria-modal="true"
      role="dialog"
      aria-labelledby="post-viewer-title"
    >
      <div
        className={`bg-notely-background rounded-lg shadow-2xl w-full max-w-6xl h-full max-h-[95vh] flex flex-col md:flex-row overflow-hidden transition-all duration-300 ease-out transform ${
          isClosing
            ? 'scale-95 opacity-0 translate-y-4'
            : isAnimating
              ? 'scale-95 opacity-0 translate-y-4'
              : 'scale-100 opacity-100 translate-y-0'
        }`}
        onClick={(e) => e.stopPropagation()} // Prevent close on content click
      >
        {/* Left Panel: Media or Text Content */}
        {/* Text-only post with elegant reading mode */}
        {localPost.mediaType === 'text' && localPost.platform !== 'pinterest' ? (
          <div className="flex-1 flex flex-col overflow-hidden bg-gray-900">
            <div className="flex-1 overflow-y-auto p-6 md:p-12 lg:p-20">
              <div className="max-w-3xl mx-auto reading-mode" style={{ backgroundColor: '#0f172a', borderRadius: '20px', padding: '50px', boxShadow: '0 8px 30px rgba(0, 0, 0, 0.3)' }}>
                <style dangerouslySetInnerHTML={{ __html: `
                  .reading-mode { 
                    font-family: Georgia, serif; 
                    color: #f3f4f6; /* Light text for dark background */
                    line-height: 1.8;
                    font-size: 1.875rem; /* Increased by 50% from 1.25rem */
                  }
                  .reading-mode h1, .reading-mode h2, .reading-mode h3 { 
                    font-family: Georgia, serif; 
                    margin-top: 1.5em;
                    margin-bottom: 0.75em;
                    font-weight: 600;
                    line-height: 1.3;
                  }
                  .reading-mode h1 { font-size: 3rem; } /* Increased by 50% */
                  .reading-mode h2 { font-size: 2.625rem; } /* Increased by 50% */
                  .reading-mode h3 { font-size: 2.25rem; } /* Increased by 50% */
                  .reading-mode p { margin-bottom: 1.5em; }
                  .reading-mode a { 
                    color: #93c5fd; /* Light blue color for links against dark blue-gray */
                    text-decoration: underline; 
                    text-decoration-thickness: 1px; 
                    text-underline-offset: 2px; 
                  }
                  .reading-mode blockquote { 
                    border-left: 4px solid #60a5fa; /* Medium blue border */
                    padding-left: 1.5rem; 
                    margin: 1.5em 0;
                    font-style: italic; 
                    color: #e5e7eb; /* Light gray text */
                    background-color: rgba(0, 0, 0, 0.2); /* Subtle darker background */
                    padding: 1rem 1.5rem;
                    border-radius: 8px;
                  }
                  .reading-mode ul, .reading-mode ol {
                    margin: 1.5em 0;
                    padding-left: 1.5em;
                  }
                  .reading-mode li {
                    margin-bottom: 0.75em;
                  }
                `}} />
                <PostContentRenderer
                  source={localPost.platform}
                  text={localPost.text || localPost.content || ''}
                  className="text-2xl md:text-3xl leading-relaxed"
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 bg-gray-900 flex items-center justify-center md:h-full h-1/3 md:min-h-0 min-h-[200px] overflow-hidden p-2 relative">
            {localPost.mediaType === 'image' && localPost.mediaUrl && (
              <>
                {/* Pinterest-specific image handling */}
                {localPost.platform === 'pinterest' ? (
                  <>
                    {console.log('[PostViewerFullScreen] Pinterest image URL:', localPost.mediaUrl)}
                    {isBase64Image(localPost.mediaUrl) ? (
                      // Directly render base64 image data for Pinterest
                      <img
                        src={localPost.mediaUrl}
                        alt={`Post by ${localPost.author}`}
                        className="max-h-full max-w-full object-contain"
                        onLoad={() => console.log('[PostViewerFullScreen] Pinterest base64 image loaded successfully')}
                        onError={(e) => {
                          console.error('[PostViewerFullScreen] Pinterest base64 image failed to load');
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : (
                      // Try direct image first, then ProxyImage as fallback
                      <img
                        src={localPost.mediaUrl}
                        alt={`Post by ${localPost.author}`}
                        className="max-h-full max-w-full object-contain"
                        onLoad={() => console.log('[PostViewerFullScreen] Pinterest direct image loaded successfully')}
                        onError={(e) => {
                          console.error('[PostViewerFullScreen] Pinterest direct image failed, trying ProxyImage');
                          // Hide the failed direct image and show ProxyImage
                          // Use type assertion to fix the style property error
                          (e.currentTarget as HTMLImageElement).style.display = 'none';
                          const proxyImageContainer = e.currentTarget.parentElement?.querySelector('.proxy-image-fallback');
                          if (proxyImageContainer) {
                            (proxyImageContainer as HTMLElement).style.display = 'block';
                          }
                        }}
                      />
                    )}
                    {/* ProxyImage fallback (initially hidden) */}
                    {!isBase64Image(localPost.mediaUrl) && (
                      <div className="proxy-image-fallback" style={{ display: 'none' }}>
                        <ProxyImage
                          src={localPost.mediaUrl}
                          alt={`Post by ${localPost.author}`}
                          className="max-h-full max-w-full object-contain"
                          postId={localPost.id}
                          onError={(error) => console.error('[PostViewerFullScreen] Pinterest ProxyImage error:', error)}
                          onLoad={() => console.log('[PostViewerFullScreen] Pinterest ProxyImage loaded successfully')}
                        />
                      </div>
                    )}
                  </>
                ) : (
                  // Standard image handling for other platforms
                  <img src={localPost.mediaUrl} alt={`Post by ${localPost.author}`} className="max-h-full max-w-full object-contain" />
                )}
              </>
            )}
            {localPost.mediaType === 'video' && localPost.mediaUrl && (
              <video src={localPost.mediaUrl} controls className="max-h-full max-w-full object-contain" />
            )}
            {localPost.mediaType === 'text' && localPost.platform === 'pinterest' && (
              <div className="h-full w-full overflow-y-auto p-6">
                <div className="text-gray-200">
                  <PostContentRenderer
                    source={localPost.platform}
                    text={localPost.text || localPost.content || ''}
                    className="text-gray-200"
                  />
                </div>
              </div>
            )}
            {!localPost.mediaUrl && localPost.mediaType !== 'text' && (
              <div className="text-gray-400">{t('postViewer.noMediaAvailable')}</div>
            )}
          </div>
        )}

        {/* Right Panel: Details - Adjusted for text-only posts */}
        <div className={`${localPost.mediaType === 'text' && localPost.platform !== 'pinterest' ? 'md:w-[300px]' : 'md:w-[360px]'} w-full bg-notely-background p-5 flex-shrink-0 overflow-y-auto border-l border-notely-border`}>
          {/* Thread Header (if in thread mode) */}
          {isThreadMode && (
            <div className="mb-4 p-3 bg-notely-accent/10 border border-notely-accent/20 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className="text-notely-accent font-semibold text-sm">🧵 {t('postViewer.thread')}</span>
                  <span className="text-xs text-notely-text-secondary">
                    {t('postViewer.threadPost').replace('{current}', String(currentThreadIndex + 1)).replace('{total}', String(threadLength))}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={handlePreviousPost}
                    disabled={!canNavigatePrevious}
                    className="p-1 text-notely-text-secondary hover:text-notely-accent disabled:opacity-30 disabled:cursor-not-allowed rounded focus:outline-none focus:ring-1 focus:ring-notely-accent"
                    title={t('postViewer.previousPost')}
                  >
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <button
                    onClick={handleNextPost}
                    disabled={!canNavigateNext}
                    className="p-1 text-notely-text-secondary hover:text-notely-accent disabled:opacity-30 disabled:cursor-not-allowed rounded focus:outline-none focus:ring-1 focus:ring-notely-accent"
                    title={t('postViewer.nextPost')}
                  >
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
              <div className="text-xs text-notely-text-tertiary">
                {t('postViewer.threadId').replace('{id}', localPost.threadId?.slice(-8) || '')}
              </div>
            </div>
          )}

          {/* Header - Simplified for text-only posts */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-2">
              <PlatformLogo platform={localPost.platform} className="w-6 h-6" />
              <div>
                <h2 id="post-viewer-title" className="text-sm font-semibold text-notely-text-primary leading-tight">{localPost.author}</h2>
                <p className="text-xs text-notely-text-secondary">
                  {formatTimestamp(localPost.timestamp)}
                  {isThreadMode && (
                    <span className="ml-2 text-notely-accent">
                      • Thread post {localPost.threadPosition || currentThreadIndex + 1}
                    </span>
                  )}
                </p>
              </div>
            </div>
            {/* Close button - X icon */}
            <button
              onClick={handleClose}
              className="p-1 text-notely-text-secondary hover:text-notely-text-primary rounded-full hover:bg-notely-surface focus:outline-none focus:ring-2 focus:ring-notely-accent"
              aria-label={t('postViewer.closePostViewer')}
            >
              <IconX />
            </button>
          </div>

          {/* Stats */}
          {localPost.stats && (localPost.stats.likes || localPost.stats.comments || localPost.stats.shares) && (
            <div className="flex items-center space-x-3 text-xs text-notely-text-secondary mb-4 pb-3 border-b border-notely-border">
              {localPost.stats.likes !== undefined && <span><strong className="text-notely-text-primary">{formatStatNumber(localPost.stats.likes)}</strong> {t('postViewer.likes')}</span>}
              {localPost.stats.comments !== undefined && <span><strong className="text-notely-text-primary">{formatStatNumber(localPost.stats.comments)}</strong> {t('postViewer.comments')}</span>}
              {localPost.stats.shares !== undefined && <span><strong className="text-notely-text-primary">{formatStatNumber(localPost.stats.shares)}</strong> {t('postViewer.shares')}</span>}
            </div>
          )}

          {/* Original Text if available and not 'text' media type (useful for captions) */}
          {localPost.mediaType !== 'text' && (localPost.text || localPost.content) && (
             <div className="mb-4">
                <h3 className="text-xs font-semibold text-notely-text-secondary uppercase tracking-wider mb-1">{t('postViewer.originalText')}</h3>
                <PostContentRenderer
                  source={localPost.platform}
                  text={localPost.text || localPost.content || ''}
                />
             </div>
          )}



          {/* SnapNote */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-1">
              <h3 className="text-xs font-semibold text-notely-text-secondary uppercase tracking-wider">{t('postViewer.snapNote')}</h3>
              <div className="flex items-center space-x-1">

                {localPost.snapNote && (
                  <button onClick={handleCopySnapNote} className="p-1 text-notely-text-secondary hover:text-notely-accent rounded focus:outline-none focus:ring-1 focus:ring-notely-accent" title={t('postViewer.copySnapNote')}>
                    <IconClipboard />
                  </button>
                )}
              </div>
            </div>

            {/* AI Error Display */}
            {aiError && (
              <div className="mb-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-xs text-red-700 dark:text-red-300">
                {aiError}
              </div>
            )}

            {/* SnapNote Content */}
            <div className="relative">
              {isAIProcessing && !localPost.snapNote && (
                <div className="flex items-center space-x-2 text-sm text-notely-text-secondary">
                  <IconRefresh className="w-4 h-4 animate-spin" />
                  <span>{t('postViewer.generatingSnapNote')}</span>
                </div>
              )}
              <p className={`text-sm text-notely-text-primary leading-relaxed ${isAIProcessing && !localPost.snapNote ? 'opacity-50' : ''}`}>
                {localPost.snapNote || (isAIProcessing ? t('postViewer.analyzingContent') : t('postViewer.aiSnapNoteAutomatic'))}
              </p>
            </div>
            {copyNotification && <p className="text-xs text-green-600 dark:text-green-400 mt-1">{copyNotification}</p>}
          </div>

          {/* InSight */}
          <div className="mb-4">
            <h3 className="text-xs font-semibold text-notely-text-secondary uppercase tracking-wider mb-2">{t('postViewer.insight')}</h3>
            {isAIProcessing && !localPost.inSight ? (
              <div className="flex items-center space-x-2 text-sm text-notely-text-secondary mb-2">
                <IconRefresh className="w-4 h-4 animate-spin" />
                <span>{t('postViewer.analyzingSentiment')}</span>
              </div>
            ) : localPost.inSight ? (
              <>
                <div className="flex items-center mb-2">
                  <span className="text-3xl mr-2">{localPost.inSight.emoji}</span>
                  <span className={`text-sm font-medium capitalize px-2 py-0.5 rounded-full ${
                    localPost.inSight.sentiment === 'positive' ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' :
                    localPost.inSight.sentiment === 'negative' ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300' :
                    'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300'
                  }`}>{localPost.inSight.sentiment}</span>
                </div>
                {localPost.inSight.contextTags && localPost.inSight.contextTags.length > 0 && (
                  <div className="flex flex-wrap gap-1.5">
                    {localPost.inSight.contextTags.map(tag => <Chip key={tag} text={formatForDisplay(tag)} interactive={false} />)}
                  </div>
                )}
              </>
            ) : (
              <div className="flex items-center mb-2">
                <span className="text-3xl mr-2">🤔</span>
                <span className="text-sm font-medium capitalize px-2 py-0.5 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300">
                  {t('postViewer.aiAnalysisAutomatic')}
                </span>
              </div>
            )}
          </div>

          {/* FastTake */}
          <div className="mb-4">
            <h3 className="text-xs font-semibold text-notely-text-secondary uppercase tracking-wider mb-1">{t('postViewer.fastTake')}</h3>
            {isAIProcessing && !localPost.fastTake ? (
              <div className="flex items-center space-x-2 text-sm text-notely-text-secondary">
                <IconRefresh className="w-4 h-4 animate-spin" />
                <span>{t('postViewer.generatingQuickInsight')}</span>
              </div>
            ) : (
              <p className="text-sm text-notely-text-primary italic">
                "{localPost.fastTake || t('postViewer.aiFastTakeAutomatic')}"
              </p>
            )}
          </div>

          {/* Tags */}
          <div className="mb-4">
            <h3 className="text-xs font-semibold text-notely-text-secondary uppercase tracking-wider mb-2">{t('postViewer.tags')}</h3>
            {onAddTag && (
              <MultiItemInput
                label={t('postViewer.addTags')}
                items={localPost.tags?.map(tag => formatForDisplay(tag)) || []}
                allItems={allTags.map(tag => formatForDisplay(tag))}
                maxItems={5}
                placeholder={t('postViewer.typeTag')}
                onChange={(newTags) => {
                  const formattedTags = newTags.map(tag => formatForStorage(tag));
                  handleTagChange(formattedTags);
                }}
              />
            )}
            {!onAddTag && localPost.tags && localPost.tags.length > 0 && (
              <div className="flex flex-wrap gap-1.5">
                {localPost.tags.map(tag => (
                  <Chip
                    key={tag}
                    text={formatForDisplay(tag)}
                    color="bg-blue-100 text-blue-700"
                    interactive={false}
                  />
                ))}
              </div>
            )}
            {!localPost.tags?.length && (
              <p className="text-xs text-notely-text-secondary italic">{t('postViewer.noTags')}</p>
            )}
          </div>

          {/* Categories */}
          <div className="mb-4">
            <h3 className="text-xs font-semibold text-notely-text-secondary uppercase tracking-wider mb-2">{t('postViewer.categories')}</h3>
            <MultiItemInput
              label={t('postViewer.addCategories')}
              items={localPost.categories?.map(cat => formatForDisplay(cat)) || []}
              allItems={allCategories.map(cat => formatForDisplay(cat))}
              maxItems={3}
              placeholder={t('postViewer.typeCategory')}
              onChange={(newCategories) => {
                const formattedCategories = newCategories.map(cat => formatForStorage(cat));
                handleCategoryChange(formattedCategories);
              }}
            />
            {!localPost.categories?.length && (
              <p className="text-xs text-notely-text-secondary italic mt-2">{t('postViewer.noCategories')}</p>
            )}
          </div>

          <hr className="my-5 border-notely-border" />

          {/* My Notes Section */}
          <div className="mt-2">
            <h3 className="text-xs font-semibold text-notely-text-secondary uppercase tracking-wider mb-2">{t('postViewer.myNotes')}</h3>
            <textarea
              rows={3}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder={t('postViewer.addNotesPlaceholder')}
              className="w-full p-2 border border-notely-border rounded-md text-sm bg-notely-background text-notely-text-primary focus:ring-1 focus:ring-notely-accent focus:border-notely-accent"
            />
            <div className="flex justify-between items-center mt-2">
              <div>
                {saveNotification && (
                  <p className={`text-xs ${saveNotification.includes('Failed') ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                    {saveNotification}
                  </p>
                )}
              </div>
              <button
                onClick={handleSaveNotes}
                disabled={isSaving}
                className="flex items-center space-x-1 px-3 py-1.5 text-sm bg-notely-accent text-white rounded-md hover:bg-notely-accent/90 focus:outline-none focus:ring-2 focus:ring-notely-accent disabled:opacity-50 disabled:cursor-not-allowed"
                title={t('postViewer.saveNotes')}
              >
                <IconSave className="w-4 h-4" />
                <span>{isSaving ? t('common.saving') : t('common.save')}</span>
              </button>
            </div>
          </div>

        </div> {/* End Right Panel */}
      </div> {/* End Main Content Box */}
    </div> // End Modal Overlay
  );
};

export default PostViewerFullScreen;