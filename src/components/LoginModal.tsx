import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  // Add prop for successful login handling
  onLoginSuccess: (token: string, user: any) => void; // Pass token and potentially user info
  preventAutoGoogleLogin?: boolean; // Flag to prevent auto-initiating Google login
}

const LoginModal: React.FC<LoginModalProps> = ({
  isOpen,
  onClose,
  onLoginSuccess, // Use the prop
  preventAutoGoogleLogin = false,
}) => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  if (!isOpen) return null;

  const handleLocalLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);
    console.log('Attempting local login with:', email);

    try {
      // Make API call to backend login endpoint
      const API_URL = 'https://api.notely.social';
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle API errors (e.g., 401 Unauthorized, 400 Bad Request)
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      // Handle success
      if (data.token) {
        // TODO: Decode token to get basic user info or fetch from /auth/me
        const placeholderUser = { displayName: 'Logged In User' }; // Replace with actual user data later
        onLoginSuccess(data.token, placeholderUser);
        onClose(); // Close modal on success
      } else {
        throw new Error('Login successful, but no token received.');
      }

    } catch (error: any) {
      console.error("Login failed:", error);
      setError(error.message || 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = () => {
    console.log('Opening Google login window...');
    setError(null);
    setIsLoading(true);

    // Flag is already set in the dashboard component
    // Just ensure it's still set here in case someone navigates away and back
    sessionStorage.setItem('acceptGoogleAuth', 'true');
    localStorage.setItem('acceptingAuthMessages', 'true');

    try {
    // Define the backend URL for Google OAuth initiation
      // Add 'prompt=select_account' to force the account selection screen
      const API_URL = process.env.API_URL || 'https://api.notely.social';
      const googleAuthUrl = `${API_URL}/auth/google?prompt=select_account`;

      // Open the URL in a new window/tab with proper styling for a Google auth popup
      const authWindow = window.open(
        googleAuthUrl,
        'googleAuthPopup',
        'width=500,height=600,resizable=yes,scrollbars=yes,status=yes'
      );

      if (!authWindow) {
        throw new Error('Popup blocked. Please allow popups for this site.');
      }

      // Clear loading state immediately since the popup is handling the process
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);

    } catch (error: any) {
      console.error('Error opening Google login popup:', error);
      setError(error.message || 'Failed to open Google login window');
      setIsLoading(false);
      // Clear auth flag if there was an error
      sessionStorage.removeItem('acceptGoogleAuth');
      localStorage.removeItem('acceptingAuthMessages');
    }
  };

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 backdrop-blur-sm"
      onClick={handleOverlayClick}
    >
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-sm mx-auto">
        <h2 className="text-xl font-semibold mb-6 text-center text-gray-800">{t('auth.login')}</h2>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {/* Email/Password Form */}
        <form onSubmit={handleLocalLogin} className="space-y-4 mb-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:opacity-50"
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:opacity-50"
            />
          </div>
          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isLoading ? t('auth.loggingIn') : t('auth.login')}
          </button>
        </form>

        {/* Divider */}
        <div className="relative mb-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or continue with</span>
          </div>
        </div>

        {/* Google Login Button */}
        <button
          onClick={handleGoogleLogin}
          disabled={isLoading}
          className="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {/* Basic Google SVG Icon */}
          <svg className="w-5 h-5 mr-2" viewBox="0 0 48 48">
             <path fill="#EA4335" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"></path>
             <path fill="#4285F4" d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"></path>
             <path fill="#FBBC05" d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z
"></path>
             <path fill="#34A853" d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"></path>
             <path fill="none" d="M0 0h48v48H0z"></path>
           </svg>
          {t('auth.loginWith')}
        </button>

        {/* TODO: Add Link to Register or include registration fields */}
        <p className="mt-6 text-center text-sm text-gray-500">
          {t('auth.noAccount')}
          <button onClick={() => alert('Registration not implemented yet')} className="font-medium text-indigo-600 hover:text-indigo-500 ml-1">
            {t('auth.register')}
          </button>
        </p>

        {/* Optional: Close button if overlay click isn't enough */}
        {/* <button onClick={onClose} className="mt-4 text-sm text-gray-600 hover:text-gray-800">Cancel</button> */}
      </div>
    </div>
  );
};

export default LoginModal;