import React, { useState, useEffect } from 'react';
import { Post } from '../types';
import { useDragAndDrop, DraggableItem } from '../hooks/useDragAndDrop';
import { generateMindstreamInsights, identifyTrendingTopics } from '../utils/mindstreamUtils';
import SavedBookmarksWidget from './SavedBookmarksWidget';
import CategoryTagManager from './CategoryTagManager';
import Widget from './Widget';

interface MindstreamWidgetsProps {
  posts: Post[];
  className?: string;
}

const MindstreamWidgets: React.FC<MindstreamWidgetsProps> = ({ posts, className = '' }) => {
  console.log('[MindstreamWidgets] Component rendered with', posts.length, 'posts');
  const [widgets, setWidgets] = useState<DraggableItem[]>([]);

  const {
    items,
    isDragging,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDrop,
    removeItem
  } = useDragAndDrop(widgets);

  // Update widgets when items change
  useEffect(() => {
    setWidgets(items);
  }, [items]);

  // Initialize default widgets
  useEffect(() => {
    const defaultWidgets: DraggableItem[] = [
      {
        id: 'category-tag-manager',
        type: 'category-tag-manager',
        position: { x: 0, y: 0 },
        size: { width: 2, height: 1 },
        data: { span: 2 }
      },
      {
        id: 'ai-insights',
        type: 'ai-insights',
        position: { x: 0, y: 1 },
        size: { width: 2, height: 1 },
        data: { span: 2 }
      },
      {
        id: 'bookmark-grid',
        type: 'bookmark-grid',
        position: { x: 0, y: 2 },
        size: { width: 2, height: 1 },
        data: { span: 2 }
      }
    ];
    setWidgets(defaultWidgets);
  }, []);

  // Get posts with links for bookmark grid
  const getBookmarkPosts = () => {
    return posts
      .filter(post => post.content && (post.content.includes('http') || post.permalink))
      .slice(0, 4);
  };

  const renderWidget = (item: DraggableItem) => {
    console.log('[MindstreamWidgets] Rendering widget:', item.type, 'with id:', item.id);
    const isDraggingThis = isDragging && item.id === items.find(i => i.id === item.id)?.id;

    const handleWidgetDragStart = (e: React.DragEvent) => {
      handleDragStart(item, e);
    };

    switch (item.type) {
      case 'category-tag-manager':
        return (
          <CategoryTagManager
            key={item.id}
            posts={posts}
            isDragging={isDraggingThis}
            onRemove={() => removeItem(item.id)}
            onDragStart={handleWidgetDragStart}
            onDragEnd={handleDragEnd}
          />
        );

      case 'ai-insights':
        const insights = generateMindstreamInsights(posts);
        const trendingTopics = identifyTrendingTopics(posts);
        return (
          <Widget
            key={item.id}
            title="🧠 AI Insights"
            span={2}
            isDragging={isDraggingThis}
            onRemove={() => removeItem(item.id)}
            onDragStart={handleWidgetDragStart}
            onDragEnd={handleDragEnd}
          >
            <div className="space-y-4">
              {/* Trending Topics */}
              {trendingTopics.length > 0 && (
                <div>
                  <h4 className="text-xs font-semibold text-notely-text-secondary mb-2 uppercase tracking-wide">Trending Topics</h4>
                  <div className="flex flex-wrap gap-2">
                    {trendingTopics.slice(0, 4).map((topic, index) => (
                      <div key={index} className="flex items-center bg-gradient-to-r from-notely-accent/10 to-notely-accent-secondary/10 rounded-notely-full px-3 py-1 border border-notely-accent/20">
                        <span className="text-xs font-medium text-notely-accent">{topic.topic}</span>
                        <span className="ml-2 text-xs text-notely-accent-secondary">+{Math.round(topic.growth)}%</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* AI Insights */}
              {insights.length > 0 && (
                <div>
                  <h4 className="text-xs font-semibold text-notely-text-secondary mb-2 uppercase tracking-wide">Smart Insights</h4>
                  <div className="space-y-2">
                    {insights.slice(0, 2).map((insight, index) => (
                      <div key={index} className="bg-notely-surface rounded-notely-md p-3 border border-notely-border notely-filter-transition hover:bg-notely-card">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs font-medium text-notely-text-primary">{insight.title}</span>
                          <div className="flex items-center">
                            <div className={`w-2 h-2 rounded-full mr-1 ${
                              insight.confidence > 0.7 ? 'bg-green-500' :
                              insight.confidence > 0.5 ? 'bg-yellow-500' : 'bg-notely-text-tertiary'
                            }`}></div>
                            <span className="text-xs text-notely-text-secondary">{Math.round(insight.confidence * 100)}%</span>
                          </div>
                        </div>
                        <p className="text-xs text-notely-text-secondary">{insight.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Fallback when no data */}
              {insights.length === 0 && trendingTopics.length === 0 && (
                <div className="text-center py-8">
                  <span className="text-4xl mb-2 block">🤖</span>
                  <p className="text-sm text-notely-text-secondary">Save more posts to see AI insights</p>
                </div>
              )}
            </div>
          </Widget>
        );

      case 'bookmark-grid':
        return (
          <SavedBookmarksWidget
            key={item.id}
            posts={posts}
            isDragging={isDraggingThis}
            onRemove={() => removeItem(item.id)}
            onDragStart={handleWidgetDragStart}
            onDragEnd={handleDragEnd}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div
      className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 ${className}`}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {items.map(renderWidget)}
    </div>
  );
};

export default MindstreamWidgets;
