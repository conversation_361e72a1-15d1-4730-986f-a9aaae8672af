import React, { useState, useEffect } from 'react';
import { Post } from '../types';
import { formatForDisplay } from '../utils/formatUtils';

interface CategorySummaryProps {
  category: string;
  posts: Post[];
  className?: string;
}

const CategorySummary: React.FC<CategorySummaryProps> = ({
  category,
  posts,
  className = ''
}) => {
  const [summary, setSummary] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (posts.length === 0) {
      setSummary('');
      return;
    }

    const generateSummary = async () => {
      setIsLoading(true);
      try {
        // Simple summary generation based on posts
        const postTexts = posts.map(p => p.content || '').filter(Boolean);
        const totalPosts = posts.length;
        const platforms = [...new Set(posts.map(p => p.platform))];
        
        // Create a simple summary without AI for now
        let summaryText = `📊 **${formatForDisplay(category)}** category contains ${totalPosts} post${totalPosts > 1 ? 's' : ''} from ${platforms.join(', ')}.`;
        
        if (postTexts.length > 0) {
          const avgLength = Math.round(postTexts.reduce((sum, text) => sum + text.length, 0) / postTexts.length);
          summaryText += `\n\n📝 Average post length: ${avgLength} characters.`;
        }

        // Add platform breakdown
        const platformCounts = platforms.map(platform => {
          const count = posts.filter(p => p.platform === platform).length;
          return `${platform}: ${count}`;
        });
        summaryText += `\n\n🔗 Platform breakdown: ${platformCounts.join(', ')}.`;

        setSummary(summaryText);
      } catch (error) {
        console.error('Error generating category summary:', error);
        setSummary(`📊 **${formatForDisplay(category)}** category contains ${posts.length} post${posts.length > 1 ? 's' : ''}.`);
      } finally {
        setIsLoading(false);
      }
    };

    generateSummary();
  }, [category, posts]);

  if (posts.length === 0) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <span className="mr-2">🏷️</span>
        {formatForDisplay(category)}
      </h3>
      
      {isLoading ? (
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span className="text-sm text-gray-500">Analyzing posts...</span>
        </div>
      ) : (
        <div className="prose prose-sm max-w-none">
          <div className="text-gray-700 text-sm leading-relaxed whitespace-pre-line">
            {summary}
          </div>
        </div>
      )}
      
      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="text-xs text-gray-500">
          {posts.length} post{posts.length > 1 ? 's' : ''} in this category
        </div>
      </div>
    </div>
  );
};

export default CategorySummary;
