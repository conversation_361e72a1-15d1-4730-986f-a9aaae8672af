import React, { useState } from 'react';

interface BaseTextProps {
  text: string;
  className?: string;
  maxLength?: number;
  showMoreText?: string;
  showLessText?: string;
}

/**
 * Base text component with consistent truncation and "More" functionality
 * All platform-specific text renderers extend this behavior
 */
export const BaseText: React.FC<BaseTextProps> = ({
  text,
  className = '',
  maxLength = 250,
  showMoreText = 'More',
  showLessText = 'Less'
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  if (!text || text.trim() === '') {
    return <div className={`text-notely-text-secondary italic ${className}`}>No text content</div>;
  }

  const needsTruncation = text.length > maxLength;
  const displayText = needsTruncation && !isExpanded 
    ? text.substring(0, maxLength).trim() 
    : text;

  const toggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={`text-sm text-notely-text-primary leading-relaxed ${className}`}>
      <span className="whitespace-pre-wrap">{displayText}</span>
      {needsTruncation && !isExpanded && (
        <>
          <span>...</span>
          <button
            onClick={toggleExpansion}
            className="text-notely-accent font-medium hover:underline ml-1 focus:outline-none focus:ring-1 focus:ring-notely-accent rounded"
            aria-label={`Show ${showMoreText.toLowerCase()}`}
          >
            {showMoreText}
          </button>
        </>
      )}
      {needsTruncation && isExpanded && (
        <button
          onClick={toggleExpansion}
          className="text-notely-accent font-medium hover:underline ml-1 focus:outline-none focus:ring-1 focus:ring-notely-accent rounded"
          aria-label={`Show ${showLessText.toLowerCase()}`}
        >
          {showLessText}
        </button>
      )}
    </div>
  );
};
