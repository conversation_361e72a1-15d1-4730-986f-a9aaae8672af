import React from 'react';
import { BaseText } from './BaseText';

interface RedditTextProps {
  text: string;
  className?: string;
}

/**
 * Reddit-specific text renderer
 * Uses standard truncation with Reddit-style "Read more" behavior
 */
export const RedditText: React.FC<RedditTextProps> = ({ text, className = '' }) => {
  return (
    <BaseText 
      text={text}
      className={className}
      maxLength={250}
      showMoreText="Read more"
      showLessText="Read less"
    />
  );
};
