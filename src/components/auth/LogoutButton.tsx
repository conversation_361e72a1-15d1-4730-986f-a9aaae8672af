import React from 'react';
import { useAuth } from '../../context/AuthContext';

const LogoutButton: React.FC = () => {
  const { logout, isLoading } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <button 
      onClick={handleLogout} 
      disabled={isLoading}
      className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
    >
      {isLoading ? 'Logging out...' : 'Log Out'}
    </button>
  );
};

export default LogoutButton; 