import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import LoginButton from './LoginButton';
import LogoutButton from './LogoutButton';

const AuthenticationForm: React.FC = () => {
  const { isAuthenticated, isLoading, user } = useAuth();

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (isAuthenticated && user) {
    return (
      <div className="p-4">
        <div className="mb-4 text-center">
          <img
            src={user.picture || 'https://via.placeholder.com/50'}
            alt="Profile"
            className="w-12 h-12 rounded-full mx-auto"
          />
          <p className="mt-2 font-semibold">{user.name}</p>
          <p className="text-sm text-gray-600">{user.email}</p>
        </div>
        <div className="text-center">
          <LogoutButton />
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold text-center mb-4">Welcome to Social Saver Pro</h2>
      <p className="text-center mb-4">Please log in to access all features</p>
      <div className="flex flex-col space-y-2">
        <LoginButton />
        <div className="text-center mt-2">
          <span className="text-sm text-gray-600">
            Don't have an account?<br/>
            <a href="#" className="text-blue-600 hover:text-blue-800" onClick={() => {
              // Open Google sign up page
              chrome.tabs.create({ url: 'https://accounts.google.com/signup' });
            }}>
              Sign up with Google
            </a>
          </span>
        </div>
      </div>
    </div>
  );
};

export default AuthenticationForm;