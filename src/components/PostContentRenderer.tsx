import React from 'react';
import { TwitterText } from './text-renderers/TwitterText';
import { RedditText } from './text-renderers/RedditText';
import { InstagramText } from './text-renderers/InstagramText';
import { PinterestText } from './text-renderers/PinterestText';
import { LinkedInText } from './text-renderers/LinkedInText';

type Platform = 'instagram' | 'x' | 'reddit' | 'linkedin' | 'pinterest' | 'X/Twitter' | 'facebook' | string;

interface PostContentRendererProps {
  source: Platform;
  text: string;
  className?: string;
}

/**
 * Central component that routes text content to platform-specific renderers
 * Each platform renderer handles its own truncation, "More" functionality, and styling
 */
export const PostContentRenderer: React.FC<PostContentRendererProps> = ({
  source,
  text,
  className = ''
}) => {
  if (!text || text.trim() === '') {
    return <div className={`text-notely-text-secondary italic ${className}`}>No text content</div>;
  }

  const platformString = String(source).toLowerCase();

  // Route to platform-specific text renderer
  if (platformString.includes('x') || platformString.includes('twitter')) {
    return <TwitterText text={text} className={className} />;
  }
  
  if (platformString.includes('reddit')) {
    return <RedditText text={text} className={className} />;
  }
  
  if (platformString.includes('instagram')) {
    return <InstagramText text={text} className={className} />;
  }
  
  if (platformString.includes('pinterest')) {
    return <PinterestText text={text} className={className} />;
  }
  
  if (platformString.includes('linkedin')) {
    return <LinkedInText text={text} className={className} />;
  }

  // Fallback for unknown platforms - use Twitter renderer as default
  return <TwitterText text={text} className={className} />;
};
