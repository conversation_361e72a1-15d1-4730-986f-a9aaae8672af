import React from 'react';
import { formatForDisplay } from '../utils/formatUtils';

interface SimpleTagSelectorProps {
  tags: string[];
  selectedTag: string | null;
  onTagSelect: (tag: string | null) => void;
  className?: string;
}

const SimpleTagSelector: React.FC<SimpleTagSelectorProps> = ({
  tags,
  selectedTag,
  onTagSelect,
  className = ''
}) => {
  if (tags.length === 0) {
    return null;
  }

  const getButtonStyles = (isSelected: boolean) => {
    const baseStyles = 'px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-250 focus:outline-none transform-gpu will-change-transform active:scale-90 relative overflow-hidden';

    if (isSelected) {
      return `${baseStyles} bg-notely-coral text-white shadow-notely-md font-semibold border-transparent hover:bg-notely-coral/90 hover:scale-110 hover:shadow-notely-lg hover:-translate-y-0.5 before:content-[''] before:absolute before:inset-0 before:bg-white/20 before:scale-0 before:rounded-full before:transition-transform before:duration-300 hover:before:scale-100`;
    }

    return `${baseStyles} bg-notely-surface text-notely-text-muted border border-notely-border hover:bg-notely-card hover:text-notely-text-secondary hover:border-notely-coral/30 hover:scale-105 hover:-translate-y-0.5 hover:shadow-sm`;
  };

  return (
    <div className={`flex flex-wrap gap-1.5 py-2 ${className}`}>
      {tags.map((tag, index) => (
        <button
          key={tag}
          onClick={() => onTagSelect(selectedTag === tag ? null : tag)}
          className={`${getButtonStyles(selectedTag === tag)} notely-tag-button`}
          style={{ animationDelay: `${index * 0.05 + 0.1}s` }}
        >
          {formatForDisplay(tag)}
        </button>
      ))}
    </div>
  );
};

export default SimpleTagSelector;
