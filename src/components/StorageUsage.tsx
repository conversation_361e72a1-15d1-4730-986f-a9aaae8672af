import React, { useState, useEffect } from 'react';

// Simple storage usage types and functions
interface StorageUsageType {
  usedMB: number;
  limitMB: number;
  usagePercentage: number;
  plan: 'free' | 'premium';
  isNearLimit: boolean;
  isOverLimit: boolean;
}

// Simple storage usage functions
const getStorageUsage = async (): Promise<StorageUsageType> => {
  // For now, return mock data since the backend storage API would be needed for real data
  return {
    usedMB: 0,
    limitMB: 1024, // 1GB for free users
    usagePercentage: 0,
    plan: 'free',
    isNearLimit: false,
    isOverLimit: false
  };
};

const getStorageUsageColor = (percentage: number): string => {
  if (percentage >= 90) return '#ef4444'; // red
  if (percentage >= 75) return '#f59e0b'; // yellow
  return '#10b981'; // green
};

const getStorageUsageMessage = (data: StorageUsageType): string => {
  if (data.isOverLimit) return 'Storage limit exceeded. Please delete some posts or upgrade to Premium.';
  if (data.isNearLimit) return 'Storage is almost full. Consider deleting old posts.';
  return 'Storage usage is healthy.';
};

const getRecommendedAction = (data: StorageUsageType): string | null => {
  if (data.isOverLimit) return 'Delete old posts or upgrade to Premium for more storage.';
  if (data.isNearLimit) return 'Consider cleaning up old posts to free up space.';
  return null;
};

interface StorageUsageProps {
  className?: string;
  showDetails?: boolean;
}

const StorageUsage: React.FC<StorageUsageProps> = ({
  className = '',
  showDetails = true
}) => {
  const [storageData, setStorageData] = useState<StorageUsageType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStorageUsage();
  }, []);

  const fetchStorageUsage = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getStorageUsage();
      setStorageData(data);
    } catch (err) {
      setError('Failed to load storage usage');
      console.error('Storage usage fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-2 bg-gray-200 rounded w-full mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (error || !storageData) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-4 ${className}`}>
        <div className="text-sm text-gray-500">
          {error || 'Storage usage unavailable'}
        </div>
      </div>
    );
  }

  const progressColor = getStorageUsageColor(storageData.usagePercentage);
  const message = getStorageUsageMessage(storageData);
  const recommendedAction = getRecommendedAction(storageData);

  return (
    <div className={`notely-card bg-notely-card border border-notely-border rounded-notely-lg shadow-notely-md hover:shadow-notely-lg transition-all duration-200 notely-breathing-md ${className}`}>
      {/* Header with enhanced visual hierarchy */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-lg">💾</span>
          <h3 className="text-sm font-semibold text-notely-text-primary notely-heading">
            Storage
          </h3>
        </div>
        <span className={`text-xs font-medium px-2 py-1 rounded-full ${
          storageData.plan === 'premium'
            ? 'bg-notely-mint/20 text-notely-mint border border-notely-mint/30'
            : 'bg-notely-surface text-notely-text-muted border border-notely-border'
        }`}>
          {storageData.plan.toUpperCase()}
        </span>
      </div>

      {/* Enhanced Progress Bar with color coding */}
      <div className="mb-4">
        <div className="flex justify-between text-xs text-notely-text-secondary mb-2 notely-body">
          <span className="font-medium">{storageData.usedMB} MB used</span>
          <span className="text-notely-text-muted">{storageData.limitMB} MB total</span>
        </div>

        {/* Progress bar with rounded corners and glow effect */}
        <div className="relative notely-progress bg-notely-surface rounded-full overflow-hidden">
          <div
            className="notely-progress-bar transition-all duration-500 ease-out rounded-full relative"
            style={{
              width: `${Math.min(storageData.usagePercentage, 100)}%`,
              backgroundColor: progressColor,
              boxShadow: storageData.usagePercentage > 75 ? `0 0 8px ${progressColor}40` : 'none'
            }}
          >
            {/* Animated shine effect for high usage */}
            {storageData.usagePercentage > 90 && (
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
            )}
          </div>
        </div>

        <div className={`text-xs mt-2 text-center font-medium notely-body ${
          storageData.isOverLimit ? 'text-notely-coral' :
          storageData.isNearLimit ? 'text-yellow-400' :
          'text-notely-text-muted'
        }`}>
          {storageData.usagePercentage}% used
        </div>
      </div>

      {/* Enhanced Message Section */}
      {showDetails && (
        <div className="space-y-3">
          <p className={`text-xs notely-body ${
            storageData.isOverLimit
              ? 'text-notely-coral'
              : storageData.isNearLimit
                ? 'text-yellow-400'
                : 'text-notely-text-secondary'
          }`}>
            {message}
          </p>

          {/* Recommended Action with better styling */}
          {recommendedAction && (
            <div className={`text-xs p-3 rounded-notely-md border-l-4 ${
              storageData.isOverLimit
                ? 'bg-notely-coral/10 text-notely-coral border-l-notely-coral border-notely-coral/20'
                : 'bg-notely-sky/10 text-notely-sky border-l-notely-sky border-notely-sky/20'
            }`}>
              <div className="flex items-start space-x-2">
                <span className="text-sm">💡</span>
                <span className="font-medium">{recommendedAction}</span>
              </div>
            </div>
          )}

          {/* Feature highlight */}
          <div className="text-xs text-notely-text-muted bg-notely-surface p-3 rounded-notely-md border border-notely-border">
            <div className="flex items-center space-x-2">
              <span className="text-sm">🖼️</span>
              <span>Images auto-preserved for deleted posts</span>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Refresh Button */}
      <button
        onClick={fetchStorageUsage}
        className={`mt-4 w-full text-xs font-medium py-2 px-3 rounded-notely-md transition-all duration-200 notely-body ${
          loading
            ? 'bg-notely-surface text-notely-text-muted cursor-not-allowed'
            : 'text-notely-sky hover:text-white hover:bg-notely-sky/20 border border-notely-sky/30 hover:border-notely-sky'
        }`}
        disabled={loading}
      >
        {loading ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="w-3 h-3 border border-notely-text-muted border-t-transparent rounded-full animate-spin"></div>
            <span>Refreshing...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center space-x-2">
            <span>🔄</span>
            <span>Refresh Storage</span>
          </div>
        )}
      </button>
    </div>
  );
};

export default StorageUsage;
