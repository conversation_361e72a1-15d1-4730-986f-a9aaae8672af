import React from 'react';
import { Post } from '../types';
import DailyWisdom from './DailyWisdom';
import WeeklyStats from './WeeklyStats';
import { formatForDisplay } from '../utils/formatUtils';

interface MindstreamSidebarProps {
  posts: Post[];
  availableCategories: string[];
  onCategoryClick: (category: string) => void;
  className?: string;
}

const MindstreamSidebar: React.FC<MindstreamSidebarProps> = ({
  posts,
  availableCategories,
  onCategoryClick,
  className = ''
}) => {
  // Calculate category usage statistics
  const getCategoryStats = () => {
    const categoryCounts: Record<string, number> = {};

    posts.forEach(post => {
      if (post.categories && Array.isArray(post.categories)) {
        post.categories.forEach(category => {
          if (category && category !== "0") {
            categoryCounts[category] = (categoryCounts[category] || 0) + 1;
          }
        });
      }
    });

    return Object.entries(categoryCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 8); // Show top 8 categories
  };

  const categoryStats = getCategoryStats();

  // Get category color based on usage
  const getCategoryColor = (count: number, maxCount: number) => {
    const intensity = count / maxCount;
    if (intensity > 0.7) return 'bg-notely-accent text-white';
    if (intensity > 0.4) return 'bg-notely-sky text-white';
    if (intensity > 0.2) return 'bg-notely-mint/20 text-notely-mint border border-notely-mint/30';
    return 'bg-notely-surface text-notely-text-secondary border border-notely-border';
  };

  const maxCount = categoryStats.length > 0 ? categoryStats[0][1] : 1;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Daily Wisdom */}
      <div>
        <DailyWisdom
          className="w-full"
          onQuoteClick={(quote) => {
            console.log('Quote clicked:', quote);
          }}
        />
      </div>

      {/* Categories Widget */}
      {categoryStats.length > 0 && (
        <div className="notely-card bg-notely-card rounded-notely-lg shadow-notely-md border border-notely-border notely-breathing-lg notely-filter-transition w-full">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-notely-text-primary notely-heading">Categories</h3>
            <div className="text-xs text-notely-text-secondary bg-notely-surface px-2 py-1 rounded-notely-full">
              {categoryStats.length} active
            </div>
          </div>

          <div className="space-y-3">
            {categoryStats.map(([category, count]) => (
              <div
                key={category}
                className="flex items-center justify-between group cursor-pointer notely-filter-transition hover:bg-notely-surface rounded-notely-md p-2 -m-2"
                onClick={() => onCategoryClick?.(category)}
              >
                <div className="flex items-center flex-1 min-w-0">
                  <div className="w-3 h-3 rounded-full bg-notely-accent mr-3 flex-shrink-0"></div>
                  <span className="text-sm font-medium text-notely-text-primary truncate group-hover:text-notely-accent transition-colors notely-body">
                    {formatForDisplay(category)}
                  </span>
                </div>
                <div className="flex items-center ml-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(count, maxCount)} transition-all group-hover:scale-105`}>
                    {count}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {availableCategories.length > categoryStats.length && (
            <div className="mt-4 pt-3 border-t border-notely-border">
              <button className="text-xs text-notely-text-secondary hover:text-notely-text-primary transition-colors">
                View all {availableCategories.length} categories →
              </button>
            </div>
          )}
        </div>
      )}

      {/* Weekly Stats */}
      <div>
        <WeeklyStats posts={posts} className="w-full" />
      </div>


    </div>
  );
};

export default MindstreamSidebar;
