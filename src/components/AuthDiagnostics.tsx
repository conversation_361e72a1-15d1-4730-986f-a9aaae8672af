import React, { useEffect, useState } from 'react';
import { diagnoseAuthIssues } from '../services/authService';
import { styled } from 'styled-components';

const DiagnosticsContainer = styled.div`
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
`;

const DiagnosticsHeader = styled.h3`
  margin-top: 0;
  color: #343a40;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 10px;
`;

const StatusBadge = styled.span<{ status: 'success' | 'error' }>`
  background-color: ${({ status }) => (status === 'success' ? '#28a745' : '#dc3545')};
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
`;

const DetailSection = styled.div`
  margin-top: 15px;
  background-color: white;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
`;

const DetailHeader = styled.h4`
  margin-top: 0;
  font-size: 16px;
  color: #495057;
`;

const KeyValueTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  
  th, td {
    text-align: left;
    padding: 8px;
    border-bottom: 1px solid #dee2e6;
  }
  
  th {
    width: 30%;
    font-weight: 500;
  }
`;

const RunButton = styled.button`
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 20px;
  
  &:hover {
    background-color: #0069d9;
  }
`;

const formatValue = (value: any): string => {
  if (value === undefined) return 'undefined';
  if (value === null) return 'null';
  if (typeof value === 'boolean') return value ? 'Yes' : 'No';
  if (typeof value === 'object') return JSON.stringify(value);
  return String(value);
};

const AuthDiagnostics: React.FC = () => {
  const [diagnostics, setDiagnostics] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const runDiagnostics = async () => {
    setLoading(true);
    try {
      const result = await diagnoseAuthIssues();
      setDiagnostics(result);
    } catch (error) {
      console.error('Failed to run diagnostics:', error);
      setDiagnostics({
        status: 'Diagnostics failed',
        details: { error: error.message }
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  return (
    <DiagnosticsContainer>
      <DiagnosticsHeader>Auth0 Authentication Diagnostics</DiagnosticsHeader>
      
      {loading && <p>Running diagnostics...</p>}
      
      {!loading && diagnostics && (
        <>
          <div>
            <strong>Status: </strong>
            <StatusBadge status={diagnostics.status.includes('valid') ? 'success' : 'error'}>
              {diagnostics.status}
            </StatusBadge>
          </div>
          
          <DetailSection>
            <DetailHeader>Configuration Check</DetailHeader>
            <KeyValueTable>
              <tbody>
                {diagnostics.details.configStatus && Object.entries(diagnostics.details.configStatus).map(([key, value]) => (
                  <tr key={key}>
                    <th>{key}</th>
                    <td>{formatValue(value)}</td>
                  </tr>
                ))}
              </tbody>
            </KeyValueTable>
          </DetailSection>
          
          <DetailSection>
            <DetailHeader>Environment</DetailHeader>
            <KeyValueTable>
              <tbody>
                <tr>
                  <th>Chrome Identity API Available</th>
                  <td>{formatValue(diagnostics.details.identityApiAvailable)}</td>
                </tr>
                <tr>
                  <th>Extension URL</th>
                  <td>{diagnostics.details.extensionUrl}</td>
                </tr>
                <tr>
                  <th>Redirect URL</th>
                  <td>{diagnostics.details.redirectUrl}</td>
                </tr>
                <tr>
                  <th>Auth0 Client Init</th>
                  <td>{diagnostics.details.clientInitStatus}</td>
                </tr>
              </tbody>
            </KeyValueTable>
          </DetailSection>
          
          <DetailSection>
            <DetailHeader>Token Status</DetailHeader>
            <KeyValueTable>
              <tbody>
                {diagnostics.details.tokenStatus && Object.entries(diagnostics.details.tokenStatus).map(([key, value]) => (
                  <tr key={key}>
                    <th>{key}</th>
                    <td>{formatValue(value)}</td>
                  </tr>
                ))}
              </tbody>
            </KeyValueTable>
          </DetailSection>
          
          <RunButton onClick={runDiagnostics}>
            Run Diagnostics Again
          </RunButton>
        </>
      )}
    </DiagnosticsContainer>
  );
};

export default AuthDiagnostics; 