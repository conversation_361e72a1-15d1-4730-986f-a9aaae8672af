import React from 'react';

interface WidgetProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  span?: 1 | 2; // Column span
  onRemove?: () => void;
  isDragging?: boolean;
  onDragStart?: (e: React.DragEvent) => void;
  onDragEnd?: (e: React.DragEvent) => void;
}

const Widget: React.FC<WidgetProps> = ({
  title,
  children,
  className = '',
  span = 1,
  onRemove,
  isDragging = false,
  onDragStart,
  onDragEnd
}) => {
  const spanClass = span === 2 ? 'col-span-2' : 'col-span-1';

  return (
    <div
      className={`
        notely-card bg-notely-card rounded-notely-lg shadow-notely-md border border-notely-border notely-breathing-lg
        ${spanClass}
        ${isDragging ? 'opacity-50 scale-95' : 'hover:shadow-notely-lg'}
        notely-filter-transition cursor-move
        ${className}
      `}
      draggable
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
    >
      {title && (
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-notely-text-primary notely-heading">{title}</h3>
          <div className="flex items-center space-x-2">
            <button className="text-notely-text-tertiary hover:text-notely-text-secondary notely-filter-transition notely-quick-action">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            {onRemove && (
              <button
                onClick={onRemove}
                className="text-notely-text-tertiary hover:text-red-500 notely-filter-transition notely-quick-action"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </div>
      )}
      {children}
    </div>
  );
};

export default Widget;
