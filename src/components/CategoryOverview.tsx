import React from 'react';
import { CategoryOverview as CategoryOverviewType } from '../types';

interface CategoryOverviewProps {
  overview: CategoryOverviewType | null;
  className?: string;
}

const CategoryOverview: React.FC<CategoryOverviewProps> = ({ overview, className = '' }) => {
  if (!overview) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (overview.isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="flex items-center mb-4">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
          <h3 className="text-lg font-semibold text-gray-900">
            Analyzing {overview.categoryName}...
          </h3>
        </div>
        <div className="animate-pulse space-y-3">
          <div className="h-4 bg-gray-200 rounded w-full"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (overview.error) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="flex items-center mb-4">
          <div className="w-5 h-5 text-red-500 mr-3">
            <svg fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">
            {overview.categoryName} Overview
          </h3>
        </div>
        <p className="text-red-600 text-sm">{overview.error}</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          {overview.categoryName} Overview
        </h3>
        <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {overview.postCount} posts
        </span>
      </div>

      {/* Summary */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Summary</h4>
        <p className="text-gray-600 text-sm leading-relaxed">
          {overview.summary}
        </p>
      </div>

      {/* Essence */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
          <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
          Why You Save This
        </h4>
        <p className="text-gray-600 text-sm leading-relaxed italic bg-purple-50 p-3 rounded-lg border-l-4 border-purple-200">
          {overview.essence}
        </p>
      </div>

      {/* Key Insights */}
      {overview.keyInsights.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Key Insights</h4>
          <ul className="space-y-2">
            {overview.keyInsights.map((insight, index) => (
              <li key={index} className="flex items-start">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-sm text-gray-600 leading-relaxed">{insight}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Patterns */}
      {overview.patterns && overview.patterns.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            Patterns
          </h4>
          <ul className="space-y-2">
            {overview.patterns.map((pattern, index) => (
              <li key={index} className="flex items-start">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-sm text-gray-600 leading-relaxed">{pattern}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* User Motivations */}
      {overview.userMotivations && overview.userMotivations.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
            Your Motivations
          </h4>
          <ul className="space-y-2">
            {overview.userMotivations.map((motivation, index) => (
              <li key={index} className="flex items-start">
                <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-sm text-gray-600 leading-relaxed">{motivation}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Thematic Tags */}
      {overview.thematicTags.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Thematic Tags</h4>
          <div className="flex flex-wrap gap-2">
            {overview.thematicTags.map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
              >
                #{tag}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-400">
          Generated {new Date(overview.generatedAt).toLocaleDateString()} at{' '}
          {new Date(overview.generatedAt).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
          })}
        </p>
      </div>
    </div>
  );
};

export default CategoryOverview;
