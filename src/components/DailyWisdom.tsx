import React, { useState, useEffect } from 'react';
import { getTodaysWisdom, addSampleQuote } from '../services/wisdomService';
import type { WisdomQuote } from '../types/wisdom';

interface DailyWisdomProps {
  className?: string;
  onQuoteClick?: (quote: WisdomQuote) => void;
}

const DailyWisdom: React.FC<DailyWisdomProps> = ({ className = '', onQuoteClick }) => {
  const [quote, setQuote] = useState<WisdomQuote | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('DailyWisdom: Component mounted, loading wisdom...');
    const loadTodaysWisdom = async () => {
      try {
        setIsLoading(true);
        console.log('DailyWisdom: Fetching today\'s wisdom...');
        const todaysQuote = await getTodaysWisdom();
        console.log('DailyWisdom: Received quote:', todaysQuote);
        setQuote(todaysQuote);
      } catch (err) {
        console.error('DailyWisdom: Failed to load daily wisdom:', err);
        setError('Failed to load daily wisdom. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    loadTodaysWisdom();
  }, []);

  const handleClick = () => {
    if (quote && onQuoteClick) {
      onQuoteClick(quote);
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-4 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-red-500 ${className}`}>
        {error}
      </div>
    );
  }

  if (!quote) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 ${className}`}>
        <div className="text-center">
          <p className="text-gray-500 dark:text-gray-400 mb-4">No wisdom quotes available.</p>
          <button
            onClick={async () => {
              try {
                const newQuote = await addSampleQuote();
                setQuote(newQuote);
              } catch (error) {
                console.error('Failed to add sample quote:', error);
                setError('Failed to add sample quote. Please try again.');
              }
            }}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
          >
            Add Sample Quote
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`notely-card bg-notely-dark-card border border-notely-dark-border rounded-notely-lg shadow-md notely-breathing-lg cursor-pointer transition-all duration-300 hover:shadow-lg hover:transform hover:-translate-y-1 ${className}`}
      onClick={handleClick}
      aria-label="Daily wisdom quote"
    >
      <div className="flex flex-col h-full">
        <div className="flex-1">
          <div className="text-notely-lavender text-sm font-medium mb-4 notely-heading tracking-wider">
            DAILY WISDOM
          </div>
          <blockquote className="notely-quote text-notely-dark-text-primary text-lg font-medium leading-relaxed mb-6">
            "{quote.text}"
          </blockquote>
          {quote.author && (
            <div className="text-right text-notely-dark-text-secondary text-sm notely-body">
              — {quote.author}
            </div>
          )}
        </div>
        <div className="mt-6 pt-4 border-t border-notely-dark-border">
          <div className="flex flex-wrap gap-2">
            {quote.categories?.slice(0, 3).map((category: string) => (
              <span
                key={category}
                className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-notely-lavender/20 text-notely-lavender border border-notely-lavender/30"
              >
                {category}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Export as both default and named export for flexibility
export { DailyWisdom };
export default DailyWisdom;
