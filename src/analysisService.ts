interface ApiKeys {
  googleCloudVision?: string;
  replicateApi?: string;
  openaiApi?: string;
  uclassifyApi?: string;
}

// IMPORTANT: API Key Management for Production
// The keys below are placeholders for development.
// For a production extension, these keys should NOT be hardcoded directly in the client-side code 
// if the code is public or easily inspectable, as this poses a security risk.
// Consider the following approaches for production:
// 1. Backend Proxy: Have your extension call your own backend server, and your backend server then makes the calls to the paid APIs using keys stored securely on the server.
// 2. Build-time Injection: If your build process allows, inject keys from environment variables at build time. This is safer than hardcoding but keys are still present in the built extension.
// 3. Fetch from Secure Endpoint: Fetch keys from a secure, authenticated endpoint you control when the extension initializes. (Less common for pure client-side extensions without a strong backend user auth).
// For this iteration, we use placeholders.
const DEVELOPER_API_KEYS: ApiKeys = {
  googleCloudVision: 'YOUR_GOOGLE_CLOUD_VISION_API_KEY_PLACEHOLDER',
  replicateApi: 'YOUR_REPLICATE_API_KEY_PLACEHOLDER',
  openaiApi: 'YOUR_OPENAI_API_KEY_PLACEHOLDER',
  uclassifyApi: 'YOUR_UCLASSIFY_API_KEY_PLACEHOLDER'
};

async function getApiKeys(): Promise<ApiKeys> {
  return new Promise((resolve) => {
    chrome.storage.sync.get([
      'googleCloudVisionApiKey',
      'replicateApiKey',
      'openaiApiKey',
      'uclassifyApiKey'
    ], (result) => {
      resolve({
        googleCloudVision: result.googleCloudVisionApiKey,
        replicateApi: result.replicateApiKey,
        openaiApi: result.openaiApiKey,
        uclassifyApi: result.uclassifyApiKey,
      });
    });
  });
}

// --- Google Cloud Vision API --- 
interface GoogleVisionLabel {
  description: string;
  score: number;
}
interface SafeSearchAnnotation {
  adult: string; // LIKELY, UNLIKELY, VERY_LIKELY, etc.
  spoof: string;
  medical: string;
  violence: string;
  racy: string;
}
interface GoogleVisionResponse {
  labels: GoogleVisionLabel[];
  safeSearch: SafeSearchAnnotation;
}

export async function analyseImageWithGoogleVision(imageUrl: string): Promise<GoogleVisionResponse> {
  const apiKey = DEVELOPER_API_KEYS.googleCloudVision;
  if (!apiKey || apiKey === 'YOUR_GOOGLE_CLOUD_VISION_API_KEY_PLACEHOLDER') {
    console.warn('Google Cloud Vision API key not found or is a placeholder.');
    // Return a default/error mock or throw
    return {
      labels: [{ description: 'mock_label_no_key', score: 0.9 }],
      safeSearch: { adult: 'UNKNOWN', spoof: 'UNKNOWN', medical: 'UNKNOWN', violence: 'UNKNOWN', racy: 'UNKNOWN' },
    };
  }
  console.log(`analyseImageWithGoogleVision called for ${imageUrl}. Key: ${apiKey.substring(0,5)}...`);
  // TODO: Implement actual API call to Google Cloud Vision (/annotate)
  // For now, returning mock data
  return {
    labels: [
      { description: "Nature", score: 0.95 },
      { description: "Landscape", score: 0.90 },
      { description: "Sky", score: 0.88 },
    ],
    safeSearch: {
      adult: "VERY_UNLIKELY",
      spoof: "VERY_UNLIKELY",
      medical: "UNLIKELY",
      violence: "VERY_UNLIKELY",
      racy: "UNLIKELY",
    },
  };
}

// --- Replicate API (CLIP) ---
interface ReplicateClipResponse {
  embedding: number[]; // 768-dim vector
  topConcepts: string[]; // Top 5 concepts
}

export async function getClipEmbeddingsAndConcepts(imageUrl: string): Promise<ReplicateClipResponse> {
  const apiKey = DEVELOPER_API_KEYS.replicateApi;
  if (!apiKey || apiKey === 'YOUR_REPLICATE_API_KEY_PLACEHOLDER') {
    console.warn('Replicate API key not found or is a placeholder.');
    return {
      embedding: Array(768).fill(0.01), // Mock embedding
      topConcepts: ['mock_concept_no_key'],
    };
  }
  console.log(`getClipEmbeddingsAndConcepts called for ${imageUrl}. Key: ${apiKey.substring(0,5)}...`);
  // TODO: Implement actual API call to Replicate (clip-vit-large-patch14)
  return {
    embedding: Array(768).fill(0.5), // Example 768-dim vector
    topConcepts: ["mountain", "lake", "sunset", "clouds", "serene"],
  };
}

// --- Replicate API (BLIP-2 for SnapNote) ---
export async function generateCaptionWithBlip2(imageUrl: string): Promise<string> {
  const apiKey = DEVELOPER_API_KEYS.replicateApi;
  if (!apiKey || apiKey === 'YOUR_REPLICATE_API_KEY_PLACEHOLDER') {
    console.warn('Replicate API key (for BLIP-2) not found or is a placeholder.');
    return 'Mock caption due to missing or placeholder API key.';
  }
  console.log(`generateCaptionWithBlip2 called for ${imageUrl}. Key: ${apiKey.substring(0,5)}...`);
  // TODO: Implement actual API call to Replicate (BLIP-2)
  return `A beautiful scenic view of ${imageUrl.substring(imageUrl.lastIndexOf('/') + 1)}.`;
}


// --- OpenAI API: GPT-4o (Vision-enabled) ---
export interface Gpt4oVisionResponse {
  snapNote?: string;
  categories?: string[]; // Should ideally be from CORE_CATEGORIES
  tags?: string[];
  rawResponse?: any; // For debugging
}

export async function callGpt4oVision(
  coreCategoriesFlat: string[], // Pass the flattened list of all possible sub-categories
  text?: string,
  imageUrl?: string,
): Promise<Gpt4oVisionResponse> {
  const apiKeys = await getApiKeys();
  const apiKey = apiKeys.openaiApi;
  if (!apiKey || apiKey === 'YOUR_OPENAI_API_KEY_PLACEHOLDER') {
    console.warn('[analysisService] OpenAI API key not found or is a placeholder for GPT-4o.');
    return { // Return a mock/default response
      snapNote: imageUrl ? 'Mock snap note for image.' : undefined,
      categories: text ? ['mock_category_from_text'] : ['mock_category_from_image'],
      tags: ['mock_tag1', 'mock_tag2'],
    };
  }

  const systemPrompt = `You are an expert content analyzer for a social media saving tool. Your task is to analyze the provided text (if any) and image (if any).
Respond ONLY with a valid JSON object with the following structure:
{
  "snapNote": "A brief, engaging, and contextually relevant micro-caption or summary. Max 150 characters. This is MANDATORY if an image is provided and the text is very short (under 20 words) or non-existent. Otherwise, it can be omitted or null.",
  "categories": ["An array of up to 3 category names. These categories MUST be chosen from the following list: ${coreCategoriesFlat.join(', ')}. If multiple from the list are relevant, pick the most specific ones. If unsure, pick the closest general category from the list."],
  "tags": ["An array of up to 6 concise keywords or 2-3 word phrases that capture the essence of the content. These tags should NOT be redundant with the chosen categories. Tags should be lowercase."]
}
If a field like snapNote is not applicable for a text-heavy post, you can omit it or set its value to null, but the JSON structure for categories (even if empty) and tags (even if empty) must be present.
Focus on accuracy and relevance. Be concise. Ensure tags are distinct from categories.`;

  const messages: any[] = [{ role: "system", content: systemPrompt }];
  const userContent: any[] = [];

  if (text && text.trim().length > 0) {
    userContent.push({ type: "text", text: text });
  }
  if (imageUrl) {
    userContent.push({ type: "image_url", image_url: { url: imageUrl, detail: "low" } });
  }
  
  if (userContent.length === 0) {
    console.warn("[analysisService] No text or image URL provided to callGpt4oVision.");
    return { categories: [], tags: [] };
  }

  messages.push({ role: "user", content: userContent });

  const payload = {
    model: "gpt-4o", 
    messages: messages,
    response_format: { type: "json_object" }, 
    max_tokens: 500, 
    temperature: 0.2, // Lower temperature for more deterministic, factual output
  };

  console.log("[analysisService] Calling GPT-4o. System Prompt Snippet:", systemPrompt.substring(0,200) + "...");


  try {
    const response = await fetchWithRetry(OPENAI_API_URL_CHAT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(payload),
    });
    const data = await response.json();

    if (data.choices && data.choices.length > 0 && data.choices[0].message?.content) {
      const contentStr = data.choices[0].message.content;
      try {
        const parsedContent = JSON.parse(contentStr) as Partial<Gpt4oVisionResponse>;
        // Validate and normalize the response
        const validatedCategories = (Array.isArray(parsedContent.categories) ? parsedContent.categories : []).filter(c => typeof c === 'string' && coreCategoriesFlat.includes(c.toLowerCase().trim())).map(c => c.toLowerCase().trim()).slice(0, 3);
        const validatedTags = (Array.isArray(parsedContent.tags) ? parsedContent.tags : []).filter(t => typeof t === 'string').map(t => t.toLowerCase().trim()).filter(t => !validatedCategories.includes(t)).slice(0, 6);
        const validatedSnapNote = (typeof parsedContent.snapNote === 'string' && parsedContent.snapNote.trim().length > 0) ? parsedContent.snapNote.trim() : undefined;
        
        const finalResponse: Gpt4oVisionResponse = {
            snapNote: validatedSnapNote,
            categories: [...new Set(validatedCategories)], // Ensure unique categories
            tags: [...new Set(validatedTags)], // Ensure unique tags
            rawResponse: data
        };
        console.log("[analysisService] GPT-4o validated content:", finalResponse);
        return finalResponse;
      } catch (jsonError) {
        console.error("[analysisService] Failed to parse JSON response from GPT-4o:", jsonError, "Raw content:", contentStr);
        throw new Error(`Failed to parse JSON from GPT-4o: ${(jsonError as Error).message}`);
      }
    } else {
      console.error("[analysisService] No content in GPT-4o response or unexpected structure:", data);
      throw new Error("No content in GPT-4o response or unexpected structure.");
    }
  } catch (error) {
    console.error('[analysisService] Error in callGpt4oVision:', error);
    return {
      snapNote: undefined,
      categories: ['error_processing'],
      tags: ['error_processing'],
      rawResponse: { error: (error as Error).message },
    };
  }
}


// --- OpenAI API: Text Embeddings (text-embedding-3-small) ---
interface OpenAiEmbeddingResponse {
  vector: number[]; 
  rawResponse?: any; // For debugging
}

export async function getOpenAiTextEmbedding(text: string): Promise<OpenAiEmbeddingResponse> {
  const apiKeys = await getApiKeys();
  const apiKey = apiKeys.openaiApi;
  if (!apiKey || apiKey === 'YOUR_OPENAI_API_KEY_PLACEHOLDER') {
    console.warn('[analysisService] OpenAI API key not found or is a placeholder for embeddings.');
    return { vector: Array(1536).fill(0.001) }; // Default for text-embedding-3-small dimensions
  }
  if (!text || text.trim().length === 0) {
    console.warn('[analysisService] Empty text provided for embedding. Returning zero vector.');
    return { vector: Array(1536).fill(0) };
  }


  const payload = {
    model: "text-embedding-3-small",
    input: text.replace(/\\n/g, " "), // API best practices suggest replacing newlines
  };

  try {
    const response = await fetchWithRetry(OPENAI_API_URL_EMBEDDINGS, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(payload),
    });
    const data = await response.json();

    if (data.data && data.data.length > 0 && data.data[0].embedding) {
      return { vector: data.data[0].embedding, rawResponse: data };
    } else {
      console.error("[analysisService] No embedding data in OpenAI response or unexpected structure:", data);
      throw new Error("No embedding data in OpenAI response.");
    }
  } catch (error) {
     console.error('[analysisService] Error in getOpenAiTextEmbedding:', error);
    return { vector: Array(1536).fill(0.002), rawResponse: { error: (error as Error).message } }; // Fallback
  }
}

// --- Utility to clean HTML (basic) ---
// This might still be useful before sending text to GPT-4o or embeddings if HTML is present.
export function cleanHtmlToText(html: string): string {
  if (!html) return '';
  try {
    // DOMParser might not be available in all service worker contexts directly without a DOM.
    // For service workers, a simpler regex-based cleaner or ensuring text is already clean might be needed.
    // However, for now, assuming it might be run in a context where it's available or text is pre-cleaned.
    if (typeof DOMParser !== 'undefined') {
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return doc.body.textContent || "";
    } else {
        // Basic regex fallback if DOMParser is not available (less robust)
        return html.replace(/<[^>]+>/g, '').replace(/\\s+/g, ' ').trim();
    }
  } catch (e) {
    console.warn("[analysisService] Error in cleanHtmlToText, returning original html", e);
    return html; // Fallback to original html if parsing fails
  }
}

// --- uClassify API (Text Classification) ---
interface UclassifyScore {
  className: string;
  score: number;
}
interface UclassifyResponse {
  scores: UclassifyScore[];
}

export async function classifyTextWithUclassify(text: string): Promise<UclassifyResponse> {
  const apiKey = DEVELOPER_API_KEYS.uclassifyApi;
  if (!apiKey || apiKey === 'YOUR_UCLASSIFY_API_KEY_PLACEHOLDER') {
    console.warn('uClassify API key not found or is a placeholder.');
    return { scores: [{ className: 'mock_category_no_key', score: 0.8 }] };
  }
  console.log(`classifyTextWithUclassify called. Key: ${apiKey.substring(0,5)}...`);
  // TODO: Implement actual API call to uClassify
  // TODO: Clean HTML from text before sending to uClassify (or do it in ************)
  return {
    scores: [
      { className: "technology", score: 0.85 },
      { className: "news", score: 0.70 },
      { className: "science", score: 0.65 },
    ],
  };
}

// --- Helper for fetch with retry and back-off ---
async function fetchWithRetry(
  url: string,
  options: RequestInit,
  retries = 3,
  backoff = 3000 // Initial backoff in ms
): Promise<Response> {
  try {
    const response = await fetch(url, options);
    if (!response.ok) {
      if (response.status === 429 && retries > 0) { // Too Many Requests
        console.warn(`[analysisService] Received 429. Retrying in ${backoff / 1000}s... (${retries} retries left)`);
        await new Promise(resolve => setTimeout(resolve, backoff));
        return fetchWithRetry(url, options, retries - 1, backoff * 2); // Exponential backoff
      }
      // For other errors, or if retries are exhausted
      const errorBody = await response.text().catch(() => 'Could not retrieve error body');
      console.error(`[analysisService] API request failed: ${response.status} ${response.statusText}`, errorBody);
      throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorBody}`);
    }
    return response;
  } catch (error) {
    if (retries > 0 && (error instanceof TypeError || (error instanceof Error && error.message.includes('Failed to fetch')))) { // Network errors or fetch-specific errors
        console.warn(`[analysisService] Network-like error. Retrying in ${backoff / 1000}s... (${retries} retries left)`);
        await new Promise(resolve => setTimeout(resolve, backoff));
        return fetchWithRetry(url, options, retries - 1, backoff * 2);
    }
    console.error('[analysisService] Fetch error:', error);
    throw error; // Re-throw if all retries fail or it's not a retryable error
  }
}

const OPENAI_API_URL_CHAT = 'https://api.openai.com/v1/chat/completions';
const OPENAI_API_URL_EMBEDDINGS = 'https://api.openai.com/v1/embeddings';

// --- Google Cloud Vision API --- 
interface GoogleVisionLabel {
  description: string;
  score: number;
}
interface SafeSearchAnnotation {
  adult: string; // LIKELY, UNLIKELY, VERY_LIKELY, etc.
  spoof: string;
  medical: string;
  violence: string;
  racy: string;
}



