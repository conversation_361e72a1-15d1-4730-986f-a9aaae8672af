import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import ReactDOM from 'react-dom/client';
import '../index.css'; // Import Tailwind CSS
import { 
  getSavedPosts, 
  getAllCategories, 
  updatePostDetails // <-- Import (replaces updatePostCategory)
} from '../storage'; 
import { Post, Platform, IUserFrontend } from '../types';
import LoginModal from '../components/LoginModal'; // Import the modal

// Components
import PostViewerFullScreen, { PostWithAIData } from '../components/PostViewerFullScreen';
import Sidebar from '../components/Sidebar'; // Import the new sidebar

// Define BACKEND_URL if not already present or ensure it's correctly defined
const BACKEND_URL = 'http://localhost:3000'; 

// App name
const APP_NAME = 'Notely';

// --- Placeholder Components ---

// --- NEW: Confirmation Modal Component ---
interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Delete Post',
  cancelText = 'Cancel',
}) => {
  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
      onClick={handleOverlayClick}
    >
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto" onClick={(e) => e.stopPropagation()}>
        <h3 className="text-lg font-semibold mb-2 text-gray-800">{title}</h3>
        <p className="text-sm text-gray-600 mb-6">{message}</p>
        <div className="flex justify-end space-x-3">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            className="px-4 py-2 rounded-md text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 transition-colors"
          >
            {cancelText}
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onConfirm();
            }}
            className="px-4 py-2 rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};
// --- END: Confirmation Modal Component ---

interface PostCardProps {
  post: Post;
  onDelete: (postId: string) => void;
  onOpenDetails: (postId: string) => void; // Prop to handle opening the detail view
  isForCapture?: boolean; // Add prop to indicate capture mode
}

// Helper to format timestamp (can be expanded)
const formatTimestamp = (isoString: string | undefined | null): string => {
  // 1. Check if input string is provided
  if (!isoString) {
    return "Unknown date"; 
  }
  
  // 2. Try parsing the date
  const date = new Date(isoString);

  // 3. Check if the parsed date is valid
  if (isNaN(date.getTime())) {
    console.warn("Invalid date string received by formatTimestamp:", isoString);
    return "Invalid date"; // Return a fallback string for invalid dates
  }

  // 4. Calculate the difference in days (ensure finite result)
  const diffInMs = date.getTime() - Date.now();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  // 5. Format only if diffInDays is a finite number
  if (!isFinite(diffInDays)) {
      console.warn("Could not calculate finite date difference for:", isoString);
      return "Some time ago"; // Fallback for non-finite diff
  }

  // 6. Proceed with formatting
  try {
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(diffInDays, 'day');
  } catch (error) {
      console.error("Error formatting relative time:", error, "Input:", isoString, "Calculated days:", diffInDays);
      return "Error formatting date"; // Fallback on formatting error
  }
};

// Format number for display (e.g., 1500 -> 1.5K)
const formatNumber = (num: number | undefined): string => {
  if (num === undefined) return '0';
  if (num === 0) return '0';
  if (num < 1000) return num.toString();
  return (num / 1000).toFixed(1) + 'K';
};

// Image Modal Component for full-screen image viewing
interface ImageModalProps {
  imageUrl: string;
  alt?: string;
  onClose: () => void;
}

const ImageModal: React.FC<ImageModalProps> = ({ imageUrl, alt, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Close modal when clicking outside the image
  const handleClickOutside = (e: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  };

  // Close modal when pressing Escape key
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    // Prevent scrolling when modal is open
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'auto';
    };
  }, []);

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="relative max-w-full max-h-full" ref={modalRef}>
        <img
          src={imageUrl}
          alt={alt || 'Full size image'}
          className="max-w-full max-h-[90vh] object-contain rounded-lg"
        />
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 bg-black/50 rounded-full text-white hover:bg-black/80 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};

const PostCard: React.FC<PostCardProps> = ({ post, onDelete, onOpenDetails, isForCapture }) => {
  const currentPlatform: Platform = post.platform; // Explicitly type the platform
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [imageDataUrls, setImageDataUrls] = useState<Record<string, string>>({});
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false); // State for confirmation modal

  // Restore the definitions for LinkedIn truncation logic
  const TRUNCATE_LENGTH = 120; // Character limit (Shortened)
  const isLinkedIn = post.platform === 'LinkedIn';
  const postContentLength = post.content?.length || 0;
  const needsTruncation = isLinkedIn && postContentLength > TRUNCATE_LENGTH;
  // End of restored definitions

  // --- Helper function to check if URL needs fetching via background ---
  // Moved outside useEffect to be accessible throughout the component
  const needsFetching = (url: string | null | undefined): boolean => {
    return !!url && url.includes('fbcdn.net'); // Only fetch fbcdn URLs for now
  };
  // --- End helper function ---

  // Handle Toggle Expansion
  const toggleExpansion = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    setIsExpanded(!isExpanded);
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsConfirmModalOpen(true);
    return false; // Explicitly return false to be absolutely sure
  };

  const confirmDelete = async () => {
    setIsConfirmModalOpen(false); // Close modal
    try {
      await onDelete(post.id);
    } catch (error) {
      console.error('Failed to delete post:', error);
    }
  };

  const cancelDelete = () => {
    setIsConfirmModalOpen(false);
  };

  // useEffect to fetch images via background script *only if needed*
  useEffect(() => {
    const urlsToFetch: string[] = [];
    const currentFetchedUrls = Object.keys(imageDataUrls);

    // Now using the needsFetching function defined outside
    // Identify avatar URL if needed
    if (needsFetching(post.authorAvatar) && !currentFetchedUrls.includes(post.authorAvatar!)) {
      urlsToFetch.push(post.authorAvatar!);
    }

    // Identify main image URL if needed
    const mainImageUrl = post.savedImage || (post.media && post.media.length > 0 ? post.media[0].url : null);
    if (needsFetching(mainImageUrl) && !currentFetchedUrls.includes(mainImageUrl!)) {
      urlsToFetch.push(mainImageUrl!);
    }

    // Fetch required URLs
    if (urlsToFetch.length > 0) {
      console.log(`[PostCard] Requesting fetch for ${urlsToFetch.length} URLs:`, urlsToFetch);
      urlsToFetch.forEach(url => {
        chrome.runtime.sendMessage({ action: 'FETCH_IMAGE', url: url }, (response) => {
          if (chrome.runtime.lastError) {
            console.error(`[PostCard] Error fetching image ${url}:`, chrome.runtime.lastError.message);
            return;
          }
          if (response?.success && response.dataUrl) {
            console.log(`[PostCard] Received data URL for: ${url}`)
            setImageDataUrls(prev => ({ ...prev, [url]: response.dataUrl }));
          } else {
            console.error(`[PostCard] Failed to fetch image ${url}:`, response?.error);
          }
        });
      });
    }

  }, [post.authorAvatar, post.savedImage, post.media, imageDataUrls]); // Added imageDataUrls to dependencies? No, keep original

  // --- Determine image sources conditionally --- 
  // Select the correct avatar URL based on what's available
  const sourceAvatarUrl = post.authorAvatar || post.authorImage; // Prefer authorAvatar, fallback to authorImage

  const needsFetchingAvatar = needsFetching(sourceAvatarUrl);
  const avatarSrc = needsFetchingAvatar 
    ? imageDataUrls[sourceAvatarUrl!] // Use data URL if fetched
    : sourceAvatarUrl; // Use original URL directly otherwise

  const mainImageUrl = post.savedImage || (post.media && post.media.length > 0 ? post.media[0].url : null);
  const needsFetchingMainImage = needsFetching(mainImageUrl);
  const mainImageSrc = needsFetchingMainImage
    ? imageDataUrls[mainImageUrl!] // Use data URL if fetched
    : mainImageUrl; // Use original URL directly otherwise

  const mainImageAlt = (post.media && post.media.length > 0 ? post.media[0].alt : '') || post.altText || '';

  // Determine if placeholders should be shown
  // const showAvatarPlaceholder = !avatarSrc; // Unused, removing
  // const showMainImagePlaceholder = !!mainImageUrl && !mainImageSrc; // Unused, removing

  // --- Handle Card Click ---
  const handleCardClick = () => {
    if (!isForCapture) { // Only allow opening details if not in capture mode
      onOpenDetails(post.id);
    }
  };

  return (
    <article 
      className="post-card relative border border-gray-200 rounded-lg shadow-sm hover:shadow-md group break-inside-avoid-column mb-4 bg-white overflow-hidden cursor-pointer transition-all duration-200 hover:border-blue-300" 
      onClick={handleCardClick}
    >
      {/* --- Platform Logo (Top Right) --- */}
      {!isForCapture && post.platform && (
        <div className="absolute top-2 right-2 z-10 opacity-70 group-hover:opacity-100 transition-opacity">
          <PlatformLogo platform={post.platform} className="w-5 h-5 text-gray-600" />
        </div>
      )}

      <div className={`${currentPlatform === 'pinterest' ? 'p-1' : 'p-4'}`}>
        {/* Header: Avatar + Name + Handle + Time - Conditionally render */}
        {currentPlatform !== 'pinterest' && (
          <div className="flex items-start">
            {/* Conditionally render Avatar section only if not Pinterest */}
            <div className="flex-shrink-0">
              {avatarSrc ? (
                // Render the actual avatar image
                <div className={`w-12 h-12 rounded-full bg-gray-200 bg-cover bg-center shadow-sm border border-gray-100`}
                     style={{ backgroundImage: `url(${avatarSrc})` }}>
                </div>
              ) : (
                // Render default grey circle placeholder for other platforms (Pinterest avatar section is hidden entirely)
                <div className="w-12 h-12 rounded-full bg-gray-200">
                </div>
              )}
            </div>

            {/* Name, Handle, Content */}
            {/* Adjust margin based on whether avatar is shown */}
            <div className={`flex-grow min-w-0 ml-3`}>
              <div className="flex items-center">
                {/* Apply flex-wrap always now */}
                <div className={`flex items-center min-w-0 flex-wrap`}>
                  {/* Conditionally apply truncate class */}
                  <span className={`font-bold text-[15px] hover:underline mr-1 ${!isForCapture ? 'truncate' : ''}`}>
                    {/* Use authorName or handle, fallback needed if both are missing */}
                    {post.authorName || post.authorHandle || 'Unknown Author'} 
                  </span>
                  {/* Conditionally render handle and timestamp if authorHandle exists */}
                  {post.authorHandle && (
                    <>
                      {/* Conditionally apply truncate class to handle */}
                      <span className={`text-gray-500 text-[15px] ${!isForCapture ? 'truncate' : ''}`}>
                        @{post.authorHandle}
                      </span>
                      <span className="text-gray-500 mx-1">·</span>
                      <span className="text-gray-500 text-[15px] hover:underline">
                        {formatTimestamp(post.timestamp || post.savedAt)}
                      </span>
                    </>
                  )}
                  {/* Render only timestamp if handle is missing but timestamp exists */}
                  {!post.authorHandle && (post.timestamp || post.savedAt) && (
                     <span className="text-gray-500 text-[15px] hover:underline ml-1"> 
                       {formatTimestamp(post.timestamp || post.savedAt)}
                     </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Media - Show image or placeholder - Render this *before* content for non-Pinterest */}
        {currentPlatform !== 'pinterest' && mainImageSrc && (
          <div className={`mt-3 grid gap-2 grid-cols-1`}>
              <div 
                className="relative rounded-xl overflow-hidden bg-gray-100 cursor-pointer shadow-sm hover:shadow-md transition-shadow duration-200"
                style={{ paddingBottom: '56.25%' }} 
                onClick={(e) => {
                  e.stopPropagation();
                  // Use the final determined src for the modal
                  if (mainImageSrc) setSelectedImage(mainImageSrc);
                }}
              >
                  <img
                    src={mainImageSrc} 
                    alt={mainImageAlt}
                    className={`absolute inset-0 w-full h-full ${isForCapture ? 'object-contain' : 'object-cover'}`}
                    loading="lazy"
                    onError={(e) => (e.currentTarget.style.display = 'none')} 
                  />
                  
              </div>
          </div>
        )}

        {/* Content for non-Pinterest (shown after images now) */}
        {currentPlatform !== 'pinterest' && post.content && (
          <div 
            className={`mt-2 font-normal text-[15px] text-gray-800 ${needsTruncation && !isExpanded ? 'line-clamp-3' : ''}`}
            // ref={contentRef} // Removed unused ref
          >
            {/* Handling for special platform-specific cases or just show content as-is */}
            {post.content}
            {needsTruncation && !isExpanded && (
              <button onClick={toggleExpansion} className="text-blue-500 font-medium hover:underline ml-1">
                Show more
              </button>
            )}
            {needsTruncation && isExpanded && (
              <button onClick={toggleExpansion} className="text-blue-500 font-medium hover:underline ml-1">
                Show less
              </button>
            )}
          </div>
        )}

        {/* Pinterest-specific render approach */}
        {currentPlatform === 'pinterest' && (
          <div className="w-full rounded-xl overflow-hidden">
            {mainImageSrc && (
              <div 
                className="relative w-full cursor-pointer hover:opacity-95 transition-opacity duration-200" 
                onClick={(e) => {
                  e.stopPropagation();
                  if (mainImageSrc) setSelectedImage(mainImageSrc);
                }}
              >
                <img
                  src={mainImageSrc}
                  alt={mainImageAlt}
                  className="w-full object-cover rounded-xl"
                  loading="lazy"
                  onError={(e) => (e.currentTarget.style.display = 'none')}
                />
                <div className="absolute bottom-2 left-2 right-2 bg-white/80 backdrop-blur-md p-2 rounded-lg text-xs line-clamp-2">
                  {post.content || ''}
                  <div className="flex items-center mt-1">
                    <PlatformLogo platform="pinterest" className="w-3 h-3 text-red-600" />
                    <span className="ml-1 text-gray-700 text-xs">
                      {post.authorName || 'Pinterest'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Engagement Metrics */}
        {!isForCapture && typeof post.interactions === 'object' && post.interactions !== null && (
          <div className="mt-2 pt-2 flex items-center text-gray-500 text-sm justify-between border-t border-gray-200">
            <div className="flex items-center">
              {/* Replies */}
              {typeof post.interactions?.replies === 'number' && (
                <div className="flex items-center mr-4 text-gray-400 hover:text-blue-500 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  {post.interactions.replies > 0 && (
                    <span>{formatNumber(post.interactions.replies)}</span>
                  )}
                </div>
              )}
              
              {/* Reposts */}
              {typeof post.interactions?.reposts === 'number' && (
                <div className="flex items-center mr-4 text-gray-400 hover:text-green-500 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  {post.interactions.reposts > 0 && (
                    <span>{formatNumber(post.interactions.reposts)}</span>
                  )}
                </div>
              )}
              
              {/* Likes */}
              {typeof post.interactions?.likes === 'number' && (
                <div className="flex items-center text-gray-400 hover:text-red-500 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  {post.interactions.likes > 0 && (
                    <span>{formatNumber(post.interactions.likes)}</span>
                  )}
                </div>
              )}
            </div>

          </div>
        )}

        {/* Action Buttons */}
        {!isForCapture && (
          <div className="mt-2 pt-1 flex justify-between items-center">
            {/* Delete Button and Original Link */}
            <div className="flex items-center space-x-4">
              <button
                onClick={handleDelete}
                className="text-red-500 hover:text-red-700 text-sm"
                title="Delete Post"
              >
                Delete
              </button>
              
              {post.permalink && (
                <a
                  href={post.permalink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
                  onClick={(e) => e.stopPropagation()}
                  title="View original post"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 16 16" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  Original
                </a>
              )}
            </div>

            {/* Categories & Tags Display */}
            {/* {Boolean(post.categories?.length || post.tags?.length) ? (
              <div className="flex flex-wrap gap-1 justify-end max-w-[70%]">
                {post.categories?.filter(cat => cat !== "0").map((cat, index) => (
                  <span key={`cat-${index}`} className="px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {cat}
                  </span>
                ))}
                {post.tags?.map((tag, index) => (
                  <span key={`tag-${index}`} className="px-2 py-0.5 bg-gray-100 text-gray-800 text-xs rounded-full">
                    #{tag}
                  </span>
                ))}
              </div>
            ) : null} */}
          </div>
        )}

      </div>

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal 
          imageUrl={selectedImage} 
          alt={mainImageAlt}
          onClose={() => setSelectedImage(null)} 
        />
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={isConfirmModalOpen}
        onClose={cancelDelete}
        onConfirm={confirmDelete}
        title="Are you sure you want to delete this post?"
        message="This will permanently remove it from your archive."
        confirmText="Delete Post"
        cancelText="Cancel"
      />
    </article>
  );
};

const SearchBar: React.FC<{ onSearch: (term: string) => void }> = ({ onSearch }) => (
  <div className="w-full relative">
    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
      <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    </div>
    <input
      type="text"
      placeholder="Search saved posts..."
      className="w-full pl-10 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 hover:bg-white transition-colors duration-150"
      onChange={(e) => onSearch(e.target.value)}
    />
  </div>
);

// Update CategoryFilter to handle array logic if needed (for filtering)
// The component itself might not need changes if filtering logic is handled in Dashboard
const CategoryFilter: React.FC<{ categories: string[], onFilter: (category: string | null) => void }> = ({ categories, onFilter }) => (
  <select
    className="p-2 h-9 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 hover:bg-white text-sm transition-colors duration-150"
    onChange={(e) => onFilter(e.target.value || null)}
    defaultValue="" // Ensure "All Categories" is selected by default
  >
    <option value="">All Categories</option>
    {/* Sort categories alphabetically for the dropdown */}
    {[...categories].sort((a, b) => a.localeCompare(b)).map(cat => <option key={cat} value={cat}>{cat}</option>)}
  </select>
);

// --- NEW: Platform Logo Component ---
const PlatformLogo: React.FC<{ platform: Platform, className?: string }> = ({ platform, className = "w-4 h-4" }) => { // Default to w-4 h-4
  // PlatformLogo ONLY passes down the className for size/layout.
  // Color (fill/stroke) is handled by individual logo components below.
  switch (platform) {
    case 'X/Twitter': return <div title="X/Twitter"><XLogo className={className} /></div>;
    case 'LinkedIn': return <div title="LinkedIn"><LinkedInLogo className={className} /></div>;
    case 'Reddit': return <div title="Reddit"><RedditLogo className={className} /></div>;
    case 'Instagram': return <div title="Instagram"><InstagramLogo className={className} /></div>;
    case 'pinterest': return <div title="Pinterest"><PinterestLogo className={className} /></div>;
    // Add cases for other platforms if needed
    default: return null;
  }
};

// --- Individual Logo SVG Components - Accept className Prop AND set their own color --- 

// X (Twitter) Logo SVG
const XLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg viewBox="0 0 24 24" className={`fill-current ${className}`} xmlns="http://www.w3.org/2000/svg">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231 5.45-6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
);

// LinkedIn Logo SVG - Using Bootstrap Icons definition
const LinkedInLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-current ${className}`} viewBox="0 0 16 16">
    <path d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248S2.4 3.226 2.4 3.934c0 .694.521 1.248 1.327 1.248zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016l.016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225z"/>
  </svg>
);

// Reddit Logo SVG - Using correct Bootstrap Icons definition
const RedditLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-current ${className}`} viewBox="0 0 16 16">
    <path d="M6.167 8a.83.83 0 0 0-.83.83c0 .459.372.84.83.831a.831.831 0 0 0 0-1.661m1.843 3.647c.315 0 1.403-.038 1.976-.611a.23.23 0 0 0 0-.306.213.213 0 0 0-.306 0c-.353.363-1.126.487-1.67.487-.545 0-1.308-.124-1.671-.487a.213.213 0 0 0-.306 0 .213.213 0 0 0 0 .306c.564.563 1.652.61 1.977.61zm.992-2.807c0 .458.373.83.831.83s.83-.381.83-.83a.831.831 0 0 0-1.66 0z"/>
    <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0m-3.828-1.165c-.315 0-.602.124-.812.325-.801-.573-1.9-.945-3.121-.993l.534-2.501 1.738.372a.83.83 0 1 0 .83-.869.83.83 0 0 0-.744.468l-1.938-.41a.2.2 0 0 0-.153.028.2.2 0 0 0-.086.134l-.592 2.788c-1.24.038-2.358.41-3.17.992-.21-.2-.496-.324-.81-.324a1.163 1.163 0 0 0-.478 2.224q-.03.17-.029.353c0 1.795 2.091 3.256 4.669 3.256s4.668-1.451 4.668-3.256c0-.114-.01-.238-.029-.353.401-.181.688-.592.688-1.069 0-.65-.525-1.165-1.165-1.165"/>
  </svg>
);

const InstagramLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} /* Removed stroke/fill attributes here */ viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
    <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
  </svg>
);

// Pinterest Logo SVG - Using the new SVG provided by user
const PinterestLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={`fill-current ${className}`} viewBox="0 0 579.148 579.148" xmlns="http://www.w3.org/2000/svg" xmlSpace="preserve">
      <path d="M434.924,38.847C390.561,12.954,342.107,0.01,289.574,0.01c-52.54,0-100.992,12.944-145.356,38.837
        C99.854,64.741,64.725,99.87,38.837,144.228C12.944,188.597,0,237.049,0,289.584c0,58.568,15.955,111.732,47.883,159.486
        c31.922,47.768,73.771,83.08,125.558,105.949c-1.01-26.896,0.625-49.137,4.902-66.732l37.326-157.607
        c-6.285-12.314-9.425-27.645-9.425-45.999c0-21.365,5.404-39.217,16.212-53.538c10.802-14.333,24.003-21.5,39.59-21.5
        c12.564,0,22.246,4.143,29.034,12.448c6.787,8.292,10.184,18.727,10.184,31.292c0,7.797-1.451,17.289-4.334,28.47
        c-2.895,11.187-6.665,24.13-11.31,38.837c-4.651,14.701-7.98,26.451-9.994,35.252c-3.525,15.33-0.63,28.463,8.672,39.4
        c9.295,10.936,21.616,16.4,36.952,16.4c26.898,0,48.955-14.951,66.176-44.865c17.217-29.914,25.826-66.236,25.826-108.973
        c0-32.925-10.617-59.701-31.859-80.312c-21.242-20.606-50.846-30.918-88.795-30.918c-42.486,0-76.862,13.642-103.123,40.906
        c-26.267,27.277-39.401,59.896-39.401,97.84c0,22.625,6.414,41.609,19.229,56.941c4.272,5.029,5.655,10.428,4.149,16.205
        c-0.508,1.512-1.511,5.281-3.017,11.309c-1.505,6.029-2.515,9.934-3.017,11.689c-2.014,8.049-6.787,10.564-14.333,7.541
        c-19.357-8.043-34.064-21.99-44.113-41.85c-10.055-19.854-15.08-42.852-15.08-68.996c0-16.842,2.699-33.685,8.103-50.527
        c5.404-16.842,13.819-33.115,25.264-48.832c11.432-15.698,25.135-29.596,41.102-41.659c15.961-12.069,35.38-21.738,58.256-29.04
        c22.871-7.283,47.51-10.93,73.904-10.93c35.693,0,67.744,7.919,96.146,23.751c28.402,15.839,50.086,36.329,65.043,61.463
        c14.951,25.135,22.436,52.026,22.436,80.692c0,37.705-6.541,71.641-19.607,101.807c-13.072,30.166-31.549,53.855-55.43,71.072
        c-23.887,17.215-51.035,25.826-81.445,25.826c-15.336,0-29.664-3.58-42.986-10.748c-13.33-7.166-22.503-15.648-27.528-25.453
        c-11.31,44.486-18.097,71.018-20.361,79.555c-4.78,17.852-14.584,38.457-29.413,61.836c26.897,8.043,54.296,12.062,82.198,12.062
        c52.534,0,100.987-12.943,145.35-38.83c44.363-25.895,79.492-61.023,105.387-105.393c25.887-44.365,38.838-92.811,38.838-145.352
        c0-52.54-12.951-100.985-38.838-145.355C514.422,99.87,479.287,64.741,434.924,38.847z"/>
  </svg>
);

const PlatformSelector: React.FC<{ onSelect: (platform: Platform | 'All') => void, selectedPlatform: Platform | 'All' }> = ({ onSelect, selectedPlatform }) => {
  // Ensure these values exactly match the Platform type in types.ts and background.ts
  const platforms: (Platform | 'All')[] = ['All', 'X/Twitter', 'LinkedIn', 'Reddit', 'Instagram', 'pinterest'];

  // --- Button Styling Logic (ASSUMED STRUCTURE - Verify if different) ---
  const getButtonClasses = (platform: Platform | 'All') => {
    const baseClasses = "flex items-center justify-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-offset-1";
    const selectedClasses = "bg-blue-600 text-white ring-blue-300";
    const unselectedClasses = "bg-white text-gray-600 hover:bg-gray-100 ring-transparent border border-gray-200";
    // Removed logoMargin, as margin is handled by the span below

    return `${baseClasses} ${selectedPlatform === platform ? selectedClasses : unselectedClasses}`;
  };
  // --- END Button Styling Logic ---

  return (
    <div className="flex space-x-2 mb-4 overflow-x-auto pb-2">
      {platforms.map((platform) => {
        // Use PlatformLogo directly here to get the rendered SVG with correct class
        const logoElement = platform !== 'All' ? <PlatformLogo platform={platform} /> : null;
        return (
          <button
            key={platform}
            onClick={() => onSelect(platform)}
            className={getButtonClasses(platform)} // Use helper for classes
          >
            {/* Render the logoElement within the span for margin */} 
            {logoElement && <span className="mr-1.5">{logoElement}</span>}
            {/* Ensure correct display name - check types.ts if it should be different */}
            {platform === 'X/Twitter' ? 'X/Twitter' :
             platform === 'LinkedIn' ? 'LinkedIn' :
             platform === 'Reddit' ? 'Reddit' :
             platform === 'Instagram' ? 'Instagram' :
             platform === 'pinterest' ? 'Pinterest' : // <-- Display name for Pinterest
             'All'}
          </button>
        );
      })}
    </div>
  );
};

// Add cog icon component
const SettingsIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <circle cx="12" cy="12" r="3"></circle>
    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
  </svg>
);

// --- Main Dashboard Component ---

function Dashboard() {
  const [posts, setPosts] = useState<Post[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<Post[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState<string | null>(null); 
  const [selectedPlatform, setSelectedPlatform] = useState<Platform | 'All'>('All');
  const [availableCategoriesForFilter, setAvailableCategoriesForFilter] = useState<string[]>([]);
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false); 
  const [isLoginModalOpen, setIsLoginModalOpen] = useState<boolean>(false);

  const [loggedInUser, setLoggedInUser] = useState<IUserFrontend | null>(null); 
  const [isUserDataLoading, setIsUserDataLoading] = useState<boolean>(true); // Start true

  // --- NEW STATE FOR POST VIEWER ---
  const [selectedPostForViewer, setSelectedPostForViewer] = useState<PostWithAIData | null>(null);
  // --- END NEW STATE ---

  // const contentRef = useRef<HTMLDivElement>(null); // Removed unused ref

  // --- MODIFIED: loadInitialData ---
  const loadInitialData = async () => {
    setIsUserDataLoading(true);
    try {
      const tokenResult = await new Promise<{ token?: string }>(resolve => 
        chrome.storage.local.get(['token'], result => resolve(result as { token?: string }))
      );
      const currentToken = tokenResult?.token;

      let fetchedPosts: Post[] = [];
      let fetchedCategories: string[] = [];

      if (currentToken) {
        setIsLoggedIn(true);
        // Fetch user data
        try {
          const userResponse = await fetch(`${BACKEND_URL}/auth/me`, {
            headers: { 'Authorization': `Bearer ${currentToken}` },
          });
          if (userResponse.ok) {
            const userData: IUserFrontend = await userResponse.json();
            setLoggedInUser(userData);
          } else {
            console.error('[Dashboard] Failed to fetch user data:', userResponse.status);
            await handleLogout(); // Await logout to ensure state is cleared before proceeding
            // After logout, fall through to load local data or show empty state
          }
        } catch (error) {
          console.error('[Dashboard] Error fetching user data:', error);
          await handleLogout();
        }
        
        // Fetch posts from cloud if logged in (even if user fetch failed, maybe posts are still accessible)
        try {
            console.log('[Dashboard] Logged in. Fetching posts from cloud...');
            const postsResponse = await fetch(`${BACKEND_URL}/api/posts`, {
                headers: { 'Authorization': `Bearer ${currentToken}` },
            });
            if (postsResponse.ok) {
                const cloudPosts: Post[] = await postsResponse.json();
                console.log('[Dashboard] loadInitialData: Successfully parsed', cloudPosts.length, 'posts from cloud. Raw data sample (first 3):', JSON.parse(JSON.stringify(cloudPosts.slice(0,3))));
                
                const validPosts = cloudPosts.filter(p => {
                    if (!p) {
                        console.warn('[Dashboard] loadInitialData: Filtering out null/undefined post object.');
                        return false;
                    }

                    const hasPlatform = p.platform && typeof p.platform === 'string';
                    const hasPermalink = p.permalink && typeof p.permalink === 'string';
                    
                    let idToUse = p.id;
                    if (p._id && (typeof p._id === 'string' || typeof p._id === 'object')) {
                        if (!(typeof idToUse === 'string' && idToUse.trim() !== '')) {
                            idToUse = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
                        }
                    }
                    const hasValidStringId = typeof idToUse === 'string' && idToUse.trim() !== '';

                    if (!hasValidStringId) {
                        console.warn(`[Dashboard] loadInitialData: Filtering out post due to missing or invalid ID. Post data (raw):`, JSON.parse(JSON.stringify(p)));
                        return false;
                    }
                    if (!hasPlatform) {
                        console.warn(`[Dashboard] loadInitialData: Filtering out post (ID: ${idToUse}) due to missing or invalid platform. Post data:`, JSON.parse(JSON.stringify(p)));
                        return false;
                    }
                    if (!hasPermalink) {
                        console.warn(`[Dashboard] loadInitialData: Filtering out post (ID: ${idToUse}) due to missing or invalid permalink. Post data:`, JSON.parse(JSON.stringify(p)));
                        return false;
                    }
                    return true; // All checks passed
                });

                fetchedPosts = validPosts.map(p => {
                    let finalId = p.id;
                    if (!(typeof finalId === 'string' && finalId.trim() !== '') && p._id) {
                        finalId = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
                    }
                    return {
                        ...p,
                        id: finalId,
                        _id: p._id
                    };
                });

                if (fetchedPosts.length < cloudPosts.length && cloudPosts.length > 0) {
                    console.warn('[Dashboard] loadInitialData: Filtered out', cloudPosts.length - fetchedPosts.length, 'of', cloudPosts.length, 'posts from cloud due to missing/invalid ID, platform, or permalink.');
                }
                console.log('[Dashboard] loadInitialData: Fetched and normalized valid posts from cloud:', fetchedPosts.length);
                // Optionally, update local IndexedDB with these cloud posts here
                // For now, just use them directly.
            } else {
                console.error('[Dashboard] Failed to fetch posts from cloud:', postsResponse.status);
                // Fallback to local posts if cloud fetch fails? Or show error?
                // For now, if cloud fails, we'll end up with empty `fetchedPosts`
                // and rely on the subsequent local fetch if the user was logged out by handleLogout.
            }
        } catch (error) {
            console.error('[Dashboard] Error fetching posts from cloud:', error);
        }
        // TODO: Fetch categories & tags from cloud if they are user-specific
        // For now, using local as they might be global or locally managed for now.
        fetchedCategories = await getAllCategories();

      } else {
        setIsLoggedIn(false);
        setLoggedInUser(null);
        console.log('[Dashboard] Not logged in. Fetching posts locally...');
        fetchedPosts = await getSavedPosts();
        fetchedCategories = await getAllCategories();
        console.log('[Dashboard] Fetched posts locally:', fetchedPosts.length);
      }

      setPosts(fetchedPosts);
      // setFilteredPosts(fetchedPosts); // This is handled by the useEffect for filtering
      setAvailableCategoriesForFilter(fetchedCategories);

    } catch (error) {
      console.error("[Dashboard] Error loading initial data:", error);
      // Ensure some default state if everything fails
      setPosts([]);
      setAvailableCategoriesForFilter([]);
    } finally {
      setIsUserDataLoading(false);
    }
  };

  useEffect(() => {
    loadInitialData();

    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }, areaName: string) => {
      if (areaName === 'local' || areaName === 'sync') { // Listen to both local and sync
        let needsRefresh = false;
        let authChanged = false;

        if (changes.token) { // Token change means auth state might have changed
          authChanged = true;
        }
        // Assuming 'savedPosts' might be a key used by older local saves, 
        // 'localSavedPosts' for explicit local saves, and cloud sync would also trigger a refresh.
        // The key 'posts' itself in storage is not standard here, rather 'savedPosts'.
        if (changes.savedPosts || changes.localSavedPosts || changes.allPosts) { // Monitor 'allPosts' if backend syncs to this key
          needsRefresh = true;
        }
        if (changes.categories) {
          // Refresh categories if they change
          getAllCategories().then(setAvailableCategoriesForFilter);
        }

        if (authChanged) {
          console.log('[Dashboard] Auth token changed, re-loading initial data...');
          setLoggedInUser(null); // Reset user to trigger fetch if new token
          loadInitialData(); // Re-run full load to check auth and fetch user/posts
        } else if (needsRefresh) {
          console.log('[Dashboard] Relevant storage changed, refreshing posts data...');
          refreshPosts(); // Just refresh post data if auth didn't change
        }
      }
    };
    chrome.storage.onChanged.addListener(handleStorageChange);

    const handleAuthMessage = (event: MessageEvent) => {
      if (event.data?.type === 'AUTH_SUCCESS' && event.data?.token) {
        chrome.storage.local.set({ token: event.data.token, authToken: event.data.token, userInfo: {} }, () => {
            // The storage listener (authChanged) will now trigger loadInitialData
            console.log('[Dashboard] AUTH_SUCCESS message received, token and authToken stored.');
            // The storage listener (authChanged) will trigger loadInitialData by itself.
            // We can call initiateLocalToCloudSync here directly after auth success.
            initiateLocalToCloudSync(); 
        });
        setIsLoginModalOpen(false);
      } else if (event.data?.type === 'AUTH_FAILURE') {
         console.error('[Dashboard] Received auth failure via postMessage:', event.data.error);
      }
    };
    window.addEventListener('message', handleAuthMessage);

    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
      window.removeEventListener('message', handleAuthMessage);
    };
  }, []); // Main useEffect runs once on mount

  // --- MODIFIED: refreshPosts with useCallback ---
  const refreshPosts = useCallback(async () => {
    console.log('[Dashboard] refreshPosts: Initiating post refresh.');
    try {
      const tokenResult = await new Promise<{ token?: string }>(resolve => 
        chrome.storage.local.get(['token'], result => resolve(result as { token?: string }))
      );
      const currentToken = tokenResult?.token;
      let refreshedPosts: Post[] = [];

      if (currentToken) {
        console.log(`[Dashboard] refreshPosts: User is logged in (token found). Fetching from cloud. Token: ${currentToken.substring(0, 20)}...`);
        const fetchUrl = `${BACKEND_URL}/api/posts`;
        console.log(`[Dashboard] refreshPosts: Fetch URL: ${fetchUrl}`);
        const response = await fetch(fetchUrl, {
          headers: { 'Authorization': `Bearer ${currentToken}` },
        });
        console.log(`[Dashboard] refreshPosts: Cloud fetch response status: ${response.status}, statusText: ${response.statusText}`);
        if (response.ok) {
          const cloudPosts: Post[] = await response.json();
          console.log(`[Dashboard] refreshPosts: Successfully parsed ${cloudPosts.length} posts from cloud response. Raw data sample (first 3):`, JSON.parse(JSON.stringify(cloudPosts.slice(0, 3))));
          
          const validIdPosts = cloudPosts.filter(p => {
            if (!p) {
              console.warn('[Dashboard] refreshPosts: Filtering out null/undefined post object.');
              return false;
            }

            const hasPlatform = p.platform && typeof p.platform === 'string';
            const hasPermalink = p.permalink && typeof p.permalink === 'string';

            let idToUse = p.id;
            if (p._id && (typeof p._id === 'string' || typeof p._id === 'object')) {
                // If p.id is not a valid string, try to use p._id (or its string representation)
                if (!(typeof idToUse === 'string' && idToUse.trim() !== '')) {
                    idToUse = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
                }
            }
            const hasValidStringId = typeof idToUse === 'string' && idToUse.trim() !== '';

            if (!hasValidStringId) {
              console.warn(`[Dashboard] refreshPosts: Filtering out post due to missing or invalid ID. Post data (raw):`, JSON.parse(JSON.stringify(p)));
              return false;
            }
            if (!hasPlatform) {
              console.warn(`[Dashboard] refreshPosts: Filtering out post (ID: ${idToUse}) due to missing or invalid platform. Post data:`, JSON.parse(JSON.stringify(p)));
              return false;
            }
            if (!hasPermalink) {
              console.warn(`[Dashboard] refreshPosts: Filtering out post (ID: ${idToUse}) due to missing or invalid permalink. Post data:`, JSON.parse(JSON.stringify(p)));
              return false;
            }
            return true; // All checks passed
          });
          
          // Map MongoDB format to frontend Post format if needed
          const normalizedPosts = validIdPosts.map(p => {
            let finalId = p.id;
            // If p.id is not a valid string, but p._id is, derive ID from p._id
            if (!(typeof finalId === 'string' && finalId.trim() !== '') && p._id) {
                finalId = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
            }
            // Ensure the final object has a valid 'id' field for the frontend.
            return {
              ...p,
              id: finalId,
              _id: p._id // keep original _id for reference if needed
            };
          });

          if (normalizedPosts.length < cloudPosts.length && cloudPosts.length > 0) { // Only log if there were posts to begin with
                console.warn(`[Dashboard] refreshPosts: Filtered out ${cloudPosts.length - normalizedPosts.length} of ${cloudPosts.length} posts from cloud due to missing/invalid ID, platform, or permalink.`);
          }
          refreshedPosts = normalizedPosts;
          console.log(`[Dashboard] refreshPosts: After ID validation and normalization, ${refreshedPosts.length} posts remain.`);
        } else {
          const errorText = await response.text().catch(() => "Failed to get error text from response");
          console.error(`[Dashboard] refreshPosts: Failed to refresh posts from cloud. Status: ${response.status}. Response text:`, errorText);
          // Optional: Fallback to local or show error
        }
      } else {
        console.log("[Dashboard] refreshPosts: User not logged in (no token). Fetching locally...");
        refreshedPosts = await getSavedPosts();
        console.log(`[Dashboard] refreshPosts: Fetched ${refreshedPosts.length} posts locally.`);
      }
      console.log(`[Dashboard] refreshPosts: Calling setPosts with ${refreshedPosts.length} posts. Data:`, JSON.parse(JSON.stringify(refreshedPosts)));
      setPosts(refreshedPosts);
    } catch (error) {
      console.error("[Dashboard] refreshPosts: General error during refresh:", error);
      setPosts([]); // Clear posts on error or show a message
    }
  }, [setPosts]); // Dependencies: setPosts. BACKEND_URL and getSavedPosts are stable.

  // Effect for listening to messages from background script
  useEffect(() => {
    const messageListener = (message: any, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => {
      console.log('[Dashboard] Message listener: Received message:', message, 'from sender:', sender);
      if (message.action === 'REFRESH_DASHBOARD_FROM_CLOUD') {
        console.log('[Dashboard] Message listener: REFRESH_DASHBOARD_FROM_CLOUD action received. Calling refreshPosts.');
        refreshPosts(); // Calling the memoized version of refreshPosts
        sendResponse({ status: 'ok', message: 'Dashboard refresh triggered by REFRESH_DASHBOARD_FROM_CLOUD.' });
        return true; // Indicate async response potential
      } else {
        console.log('[Dashboard] Message listener: Received unhandled message action:', message.action);
      }
    };
    console.log('[Dashboard] Adding REFRESH_DASHBOARD_FROM_CLOUD listener.');
    chrome.runtime.onMessage.addListener(messageListener);
    return () => {
      console.log('[Dashboard] Removing REFRESH_DASHBOARD_FROM_CLOUD listener.');
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, [refreshPosts]); // Dependency array now only contains the memoized refreshPosts

  const handleUpdatePostDetails = async (postId: string, details: { categories?: string[], tags?: string[] }) => {
    try {
      await updatePostDetails(postId, details);
      // Optimistically update local state
      setPosts(prevPosts =>
        prevPosts.map(p =>
          p.id === postId ? { 
              ...p, 
              ...(details.categories !== undefined && { categories: details.categories }),
              ...(details.tags !== undefined && { tags: details.tags }),
           } : p
        )
      );
      // If the post viewer is open and this is the selected post, update its data too
      if (selectedPostForViewer && selectedPostForViewer.id === postId) {
        setSelectedPostForViewer(prevSelectedPost => {
          if (!prevSelectedPost) return null;
          // Update with flattened structure
          return {
            ...prevSelectedPost,
            ...(details.categories !== undefined && { categories: details.categories }),
            ...(details.tags !== undefined && { tags: details.tags }),
          };
        });
      }
      console.log(`[Dashboard] Updated details for post ${postId}:`, details);
    } catch (error) {
      console.error(`[Dashboard] Failed to update details for post ${postId}:`, error);
      // Consider reverting optimistic update or showing error message
    }
  };

  // --- MODIFIED: handleOpenPostDetails to use PostViewerFullScreen ---
  const handleOpenPostDetails = (postId: string) => {
    const postToView = posts.find(p => p.id === postId);
    if (postToView) {
      console.log(`[Dashboard] Opening PostViewerFullScreen for post: ${postId}`);
      
      let determinedMediaType: 'image' | 'video' | 'text';
      // Check if postToView itself has a valid mediaType property first
      if (postToView.hasOwnProperty('mediaType')) {
        const existingMediaType = (postToView as any).mediaType as string;
        if (existingMediaType === 'image' || existingMediaType === 'video' || existingMediaType === 'text') {
          determinedMediaType = existingMediaType;
        } else if (postToView.savedImage || (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('image'))) {
          determinedMediaType = 'image';
        } else if (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('video')) {
          determinedMediaType = 'video';
        } else {
          determinedMediaType = 'text'; // Default if specific checks fail
        }
      } else { // Fallback logic if mediaType property doesn't exist on postToView
        if (postToView.savedImage || (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('image'))) {
          determinedMediaType = 'image';
        } else if (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('video')) {
          determinedMediaType = 'video';
        } else {
          determinedMediaType = 'text';
        }
      }

      // Safely access potential existing AI data (assuming it might be flat on postToView already or undefined)
      const snapNote = (postToView as any).snapNote;
      const inSight = (postToView as any).inSight;
      const fastTake = (postToView as any).fastTake;
      const postTags = (postToView as any).tags;
      const postCategories = (postToView as any).categories;
      const contentIdeas = (postToView as any).contentIdeas;

      const postForViewer: PostWithAIData = {
        id: postToView.id,
        platform: postToView.platform,
        mediaType: determinedMediaType,
        mediaUrl: postToView.savedImage || (postToView.media && postToView.media.length > 0 ? postToView.media[0].url : undefined),
        text: postToView.content,
        author: postToView.authorName || postToView.authorHandle || 'Unknown Author',
        timestamp: postToView.timestamp || postToView.savedAt || new Date().toISOString(),
        stats: postToView.interactions ? {
          likes: postToView.interactions.likes,
          comments: postToView.interactions.replies,
          shares: postToView.interactions.reposts,
        } : undefined,
        // Flattened AI fields
        snapNote: snapNote || 'AI SnapNote pending.',
        inSight: {
          sentiment: (
            inSight?.sentiment &&
            ['positive', 'neutral', 'negative'].includes(inSight.sentiment)
          ) ? inSight.sentiment as 'positive' | 'neutral' | 'negative' : 'neutral',
          emoji: typeof inSight?.emoji === 'string' ? inSight.emoji : '🤔',
          contextTags: Array.isArray(inSight?.contextTags) ? inSight.contextTags.filter((tag: any) => typeof tag === 'string') : [],
        },
        fastTake: fastTake || 'AI FastTake pending.',
        tags: postTags || [], // Use 'tags'
        categories: postCategories || [],
        contentIdeas: contentIdeas || [
          'Engage with a question.',
          'Share a surprising fact.',
          'Post a poll.'
        ],
      };
      setSelectedPostForViewer(postForViewer);
    } else {
      console.warn(`[Dashboard] Post with ID ${postId} not found for viewer.`);
    }
  };
  // --- END MODIFIED handleOpenPostDetails ---

  // --- NEW HANDLERS FOR POST VIEWER ---
  const handleClosePostViewer = () => {
    setSelectedPostForViewer(null);
  };

  const handleAddCategoryToPost = (postId: string, category: string) => {
    console.log(`[Dashboard] Placeholder: Add category '${category}' to post ${postId}`);
    // Find the post and update its categories
    const postToUpdate = posts.find(p => p.id === postId);
    if (postToUpdate) {
      const updatedCategories = Array.from(new Set([...(selectedPostForViewer?.categories || []), category]));
      handleUpdatePostDetails(postId, { categories: updatedCategories });
    }
  };

  const handleRemoveTagFromPost = (postId: string, tagToRemove: string) => {
    console.log(`[Dashboard] Placeholder: Remove tag '${tagToRemove}' from post ${postId}`);
    const postToUpdate = posts.find(p => p.id === postId);
     if (postToUpdate) {
      const updatedTags = (selectedPostForViewer?.tags || []).filter((tag: string) => tag !== tagToRemove); // Use .tags and type 'tag'
      handleUpdatePostDetails(postId, { tags: updatedTags });
    }
  };
  
  const handleRemoveCategoryFromPost = (postId: string, categoryToRemove: string) => {
    console.log(`[Dashboard] Placeholder: Remove category '${categoryToRemove}' from post ${postId}`);
    const postToUpdate = posts.find(p => p.id === postId);
    if (postToUpdate) {
      const updatedCategories = (selectedPostForViewer?.categories || []).filter(cat => cat !== categoryToRemove);
      handleUpdatePostDetails(postId, { categories: updatedCategories });
    }
  };
  // --- END NEW HANDLERS ---

  const handleDeletePost = async (postId: string) => {
    try {
      const tokenResult = await new Promise<{ authToken?: string }>(resolve => // Expect authToken in promise type
        chrome.storage.local.get(['authToken'], result => resolve(result as { authToken?: string })) // Get 'authToken'
      );
      const currentToken = tokenResult?.authToken; // Use authToken

      if (currentToken) {
        // User is logged in, proceed with cloud deletion
        console.log(`[Dashboard] Attempting to delete post ${postId} via background script (cloud deletion).`);
        chrome.runtime.sendMessage(
          { 
            action: 'DELETE_POST_FROM_CLOUD', 
            postId: postId, 
            token: currentToken 
          },
          (response) => {
            if (chrome.runtime.lastError) {
              console.error('[Dashboard] handleDeletePost (cloud): Error sending message to background:', chrome.runtime.lastError.message);
              alert(`Error deleting post: ${chrome.runtime.lastError.message}`);
              refreshPosts(); 
              return;
            }
            if (response?.status === 'success') {
              console.log(`[Dashboard] handleDeletePost (cloud): Successfully deleted post ${postId} (via background).`);
              refreshPosts(); 
            } else {
              console.error(`[Dashboard] handleDeletePost (cloud): Failed to delete post ${postId} (via background). Response:`, response);
              // Check if the error suggests the post wasn't found on the cloud or bad ID
              // This is an assumption about the error structure; adjust if backend sends different signals
              const isLikelyNotFoundError = 
                (response?.message && (response.message.includes('not found') || response.message.includes('Invalid post ID'))) ||
                (response?.details?.status === 404) || // Assuming details might contain backend status
                (response?.details?.status === 400); // Assuming 400 for invalid ID

              if (isLikelyNotFoundError) {
                console.log(`[Dashboard] handleDeletePost (cloud): Cloud deletion failed (likely not found/invalid ID). Attempting local deletion for ${postId}.`);
                // Fallback to local deletion
                chrome.runtime.sendMessage(
                  { 
                    action: 'DELETE_POST_LOCALLY', 
                    postId: postId 
                  },
                  (localResponse) => {
                    if (chrome.runtime.lastError) {
                      console.error('[Dashboard] handleDeletePost (local fallback): Error sending message:', chrome.runtime.lastError.message);
                      alert(`Error deleting locally after cloud fail: ${chrome.runtime.lastError.message}`);
                    } else if (localResponse?.status === 'success') {
                      console.log(`[Dashboard] handleDeletePost (local fallback): Successfully deleted post ${postId} locally.`);
                    } else {
                      console.error(`[Dashboard] handleDeletePost (local fallback): Failed to delete post ${postId} locally. Response:`, localResponse);
                      alert(`Failed to delete post locally after cloud attempt: ${localResponse?.message || 'Unknown error.'}`);
                    }
                    refreshPosts(); // Refresh regardless of fallback outcome
                  }
                );
              } else {
                // For other cloud errors, just alert the original cloud error message
                alert(`Failed to delete post from cloud: ${response?.message || 'Unknown error from background script.'}`);
                refreshPosts(); 
              }
            }
          }
        );
      } else {
        // User is logged out, proceed with local deletion
        console.log(`[Dashboard] Attempting to delete post ${postId} locally (user logged out).`);
        chrome.runtime.sendMessage(
          { 
            action: 'DELETE_POST_LOCALLY', 
            postId: postId 
          },
          (response) => {
            if (chrome.runtime.lastError) {
              console.error('[Dashboard] handleDeletePost (local): Error sending message to background:', chrome.runtime.lastError.message);
              alert(`Error deleting locally saved post: ${chrome.runtime.lastError.message}`);
              refreshPosts();
              return;
            }
            if (response?.status === 'success') {
              console.log(`[Dashboard] handleDeletePost (local): Successfully deleted post ${postId} locally (via background).`);
              refreshPosts();
            } else {
              console.error(`[Dashboard] handleDeletePost (local): Failed to delete post ${postId} locally (via background). Response:`, response);
              alert(`Failed to delete locally saved post: ${response?.message || 'Unknown error from background script.'}`);
              refreshPosts();
            }
          }
        );
      }
    } catch (error) {
      console.error('[Dashboard] handleDeletePost: General error:', error);
      alert(`An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`);
      refreshPosts(); // Refresh to ensure UI consistency even on error
    }
  };

  useEffect(() => {
    let sortedPosts = [...posts];

    // Sort posts by timestamp or savedAt date
    sortedPosts.sort((a, b) => {
      const dateA = new Date(a.timestamp || a.savedAt || 0).getTime(); // Added fallback for safety
      const dateB = new Date(b.timestamp || b.savedAt || 0).getTime(); // Added fallback for safety
      // Handle potential NaN dates
      if (isNaN(dateA) && isNaN(dateB)) return 0;
      if (isNaN(dateA)) return 1; // Put posts without valid dates last
      if (isNaN(dateB)) return -1;
      return dateB - dateA; // Descending order
    });

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      sortedPosts = sortedPosts.filter(post =>
        post.content?.toLowerCase().includes(term) ||
        post.authorName?.toLowerCase().includes(term) ||
        post.authorHandle?.toLowerCase().includes(term) ||
        post.categories?.some(cat => cat.toLowerCase().includes(term)) || // Search categories
        post.tags?.some(tag => tag.toLowerCase().includes(term)) // Search tags
      );
    }

    if (selectedCategoryFilter) {
      sortedPosts = sortedPosts.filter(post => 
        post.categories && post.categories.includes(selectedCategoryFilter)
      );
    }

    if (selectedPlatform !== 'All') {
      console.log(`[Dashboard Filter] Filtering by platform: ${selectedPlatform}`);
      sortedPosts = sortedPosts.filter(post => {
        const postPlatformLower = post.platform?.toLowerCase();
        const selectedPlatformLower = selectedPlatform.toLowerCase();
        // console.log(`[Dashboard Filter DEBUG] Comparing: '${postPlatformLower}' === '${selectedPlatformLower}'`);
        return postPlatformLower === selectedPlatformLower; 
      });
    }

    setFilteredPosts(sortedPosts);
  }, [posts, searchTerm, selectedCategoryFilter, selectedPlatform]); 

  const processedPosts = useMemo(() => {
    return filteredPosts;
  }, [filteredPosts]);

  const handleOpenLoginModal = () => {
    sessionStorage.setItem('acceptGoogleAuth', 'true');
    localStorage.setItem('acceptingAuthMessages', 'true');
    setIsLoginModalOpen(true);
  };

  const handleCloseLoginModal = () => {
    if (!isLoggedIn) {
      sessionStorage.removeItem('acceptGoogleAuth');
      localStorage.removeItem('acceptingAuthMessages');
    }
    setIsLoginModalOpen(false);
  };

  const handleLogout = async () => {
    sessionStorage.removeItem('acceptGoogleAuth');
    localStorage.removeItem('acceptingAuthMessages');
    await new Promise<void>(resolve => chrome.storage.local.remove(['token', 'userInfo'], () => resolve()));
    setIsLoggedIn(false);
    setLoggedInUser(null); 
    console.log("[Dashboard] User logged out. Reloading data (should fetch local).");
    await loadInitialData(); // Reload data, which will now fetch local posts
  };

  const handleLoginSuccess = (token: string, _user: any) => { // User param no longer needed from modal
    console.log('Login successful! JWT received in dashboard (from postMessage or direct call).');
    chrome.storage.local.set({ token: token, authToken: token, userInfo: {} /* Clear old userInfo, /auth/me is source of truth */ }, () => {
      // The storage listener (authChanged) will trigger loadInitialData.
      // Call sync after setting token.
      initiateLocalToCloudSync();
    });
    setIsLoginModalOpen(false); 
  };

  const handleUpgradeToPremium = async () => {
    if (!loggedInUser) {
      console.error("User not logged in, cannot upgrade.");
      return;
    }
    chrome.storage.local.get(['token'], async (result) => {
      if (!result.token) {
        console.error("No token found for upgrade.");
        handleOpenLoginModal(); // Prompt login
        return;
      }
      try {
        // Defaulting to 'monthly'. UI can be added later to select plan.
        const response = await fetch('http://localhost:3000/auth/stripe/create-checkout-session', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${result.token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ plan: 'monthly' }), 
        });

        if (response.ok) {
          const { url: stripeCheckoutUrl } = await response.json();
          if (stripeCheckoutUrl) {
            window.location.href = stripeCheckoutUrl;
          } else {
            console.error('Stripe checkout URL not received.');
            // TODO: Show error to user
          }
        } else {
          const errorData = await response.json();
          console.error('Failed to create Stripe checkout session:', response.status, errorData);
          // TODO: Show error to user (e.g., errorData.message)
        }
      } catch (error) {
        console.error('Error creating Stripe checkout session:', error);
        // TODO: Show error to user
      }
    });
  };

  const initiateLocalToCloudSync = async () => {
    console.log('[Dashboard] initiateLocalToCloudSync: Checking for local posts to sync...');
    const localPostsToSync = await getSavedPosts(); // From '../storage'
    
    if (localPostsToSync && localPostsToSync.length > 0) {
      console.log(`[Dashboard] initiateLocalToCloudSync: Found ${localPostsToSync.length} local posts. Attempting to sync...`);
      // For now, let's try to sync them one by one without complex queueing first
      // We'll rely on the backend to handle duplicates (409 conflict)
      // A more robust solution would involve checking if a post *needs* syncing (e.g., no cloud ID yet)

      const tokenResult = await new Promise<{ authToken?: string }>(resolve =>
        chrome.storage.local.get(['authToken'], result => resolve(result as { authToken?: string }))
      );
      const currentToken = tokenResult?.authToken;

      if (!currentToken) {
        console.warn('[Dashboard] initiateLocalToCloudSync: No auth token found. Cannot sync local posts.');
        return;
      }

      for (const post of localPostsToSync) {
        // We need to ensure the post object sent to SAVE_POST_CLOUD is what the background script expects.
        // The raw post from getSavedPosts() might be slightly different from what content.ts sends.
        // For now, assume it's compatible or background script's generateAnalyzedPost handles it.
        console.log(`[Dashboard] initiateLocalToCloudSync: Syncing post ${post.id} to cloud.`);
        chrome.runtime.sendMessage(
          {
            action: 'SAVE_POST_CLOUD',
            data: post, // Send the local post data
            token: currentToken
          },
          (response) => {
            if (chrome.runtime.lastError) {
              console.error(`[Dashboard] initiateLocalToCloudSync: Error syncing post ${post.id}:`, chrome.runtime.lastError.message);
              // Handle error, maybe retry later or notify user
            } else if (response?.status === 'success' || response?.status === 'duplicate') {
              console.log(`[Dashboard] initiateLocalToCloudSync: Post ${post.id} synced/already exists. Status: ${response.status}`);
              // Optionally, update local post with cloud ID or mark as synced
            } else {
              console.warn(`[Dashboard] initiateLocalToCloudSync: Failed to sync post ${post.id}. Response:`, response);
              // Handle failure
            }
          }
        );
        // Add a small delay to avoid overwhelming the background script/backend
        await new Promise(resolve => setTimeout(resolve, 500)); // 0.5 second delay between sync attempts
      }
      console.log('[Dashboard] initiateLocalToCloudSync: Finished sync attempt for all local posts.');
      refreshPosts(); // Refresh dashboard to show newly synced posts from cloud
    } else {
      console.log('[Dashboard] initiateLocalToCloudSync: No local posts found to sync.');
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar 
        user={{
          name: loggedInUser?.profileSettings?.name || loggedInUser?.email || undefined,
          profilePicture: loggedInUser?.profileSettings?.picture || undefined
        }}
        onNavigate={(section) => {
          console.log(`Navigating to ${section}`);
          if (section === 'logout') {
            handleLogout();
          }
        }}
        activeSection="discover"
        newWisdomCount={3} // Example count, replace with actual data
      />
      
      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <header className="sticky top-0 z-10 bg-white/70 backdrop-blur-md border-b border-gray-200/50 shadow-sm">
          <div className="px-4 py-3 flex justify-between items-center"> 
            <h1 className="text-lg font-bold text-gray-800 flex items-center">
              <img src="/notely.svg" alt="Notely" className="w-6 h-6 mr-2" />
              <span>Notely</span>
            </h1>
            <div className="flex items-center space-x-4">
              {isUserDataLoading && <p className="text-sm text-gray-500">Loading account...</p>}
              {!isUserDataLoading && isLoggedIn && loggedInUser ? (
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <div className="flex items-center">
                      <p className="text-sm font-semibold text-gray-800">{loggedInUser.name}</p>
                      <a href="settings.html" className="ml-2" title="Settings">
                    <div className="flex items-center space-x-3">
                      <div className="text-right">
                        <div className="flex items-center">
                          <p className="text-sm font-semibold text-gray-800">{loggedInUser.name}</p>
                          <a href="settings.html" className="ml-2" title="Settings">
                            <SettingsIcon className="w-4 h-4 text-gray-600 hover:text-blue-600 transition-colors duration-150" />
                          </a>
                        </div>
                        <p className="text-xs text-gray-500">{loggedInUser.email}</p>
                        <p className="text-xs">
                          Plan: <span className={`font-semibold ${loggedInUser.plan === 'premium' ? 'text-green-600' : 'text-blue-600'}`}>
                            {loggedInUser.plan === 'premium' ? 'Premium' : 'Free'}
                          </span>
                        </p>
                        {loggedInUser.plan === 'free' && (
                          <button
                            onClick={handleUpgradeToPremium}
                            className="mt-1 px-2 py-0.5 bg-green-500 text-white rounded hover:bg-green-600 text-xs shadow-sm transition-colors duration-150"
                          >
                            Upgrade to Premium
                          </button>
                        )}
                        {loggedInUser.plan === 'premium' && (
                            <p className="mt-1 text-xs text-gray-500 italic">Premium Active</p>
                            // TODO: Link to Stripe customer portal if available
                        )}
                      </div>
                      {loggedInUser.profileSettings?.picture && (
                        <img src={loggedInUser.profileSettings.picture} alt="Profile" className="w-10 h-10 rounded-full border-2 border-gray-200" />
                      )}
                      <button 
                        onClick={handleLogout}
                        className="px-3 py-1.5 rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150"
                      >
                        Logout
                      </button>
                    </div>
                  ) : (
                    !isUserDataLoading && (
                    <div className="flex items-center space-x-3">
                      <a href="settings.html" className="mr-2" title="Settings">
                        <SettingsIcon className="w-4 h-4 text-gray-600 hover:text-blue-600 transition-colors duration-150" />
                      </a>
                      <button 
                        onClick={handleOpenLoginModal}
                        className="px-3 py-1.5 rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
                        {loggedInUser.plan === 'premium' ? 'Premium' : 'Free'}
                      </span>
                    </p>
                    {loggedInUser.plan === 'free' && (
                      <button
                        onClick={handleUpgradeToPremium}
                        className="mt-1 px-2 py-0.5 bg-green-500 text-white rounded hover:bg-green-600 text-xs shadow-sm transition-colors duration-150"
                      >
                        Upgrade to Premium
                      </button>
                    )}
                    {loggedInUser.plan === 'premium' && (
                        <p className="mt-1 text-xs text-gray-500 italic">Premium Active</p>
                        // TODO: Link to Stripe customer portal if available
                    )}
                  </div>
                  {loggedInUser.profileSettings?.picture && (
                    <img src={loggedInUser.profileSettings.picture} alt="Profile" className="w-10 h-10 rounded-full border-2 border-gray-200" />
                  )}
                  <button 
                    onClick={handleLogout}
                    className="px-3 py-1.5 rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150"
                  >
                    Logout
                  </button>
                </div>
              ) : (
                !isUserDataLoading && (
                <div className="flex items-center space-x-3">
                  <a href="settings.html" className="mr-2" title="Settings">
                    <SettingsIcon className="w-4 h-4 text-gray-600 hover:text-blue-600 transition-colors duration-150" />
                  </a>
                  <button 
                    onClick={handleOpenLoginModal}
                    className="px-3 py-1.5 rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
                  >
                    Login
                  </button>
                </div>
                )
              )}
            </div>
          </div>
          <div className="px-4 pb-4 flex flex-row items-center justify-between gap-4">
            <div className="w-auto">
              <PlatformSelector onSelect={setSelectedPlatform} selectedPlatform={selectedPlatform} />
              {availableCategoriesForFilter.length > 0 && (
                <div className="flex items-center space-x-2 mt-2">
                  <span className="text-sm font-medium text-gray-600">Filter:</span>
                  <CategoryFilter categories={availableCategoriesForFilter} onFilter={setSelectedCategoryFilter} /> 
                </div>
              )}
            </div>
            <div className="w-1/3">
              <SearchBar onSearch={setSearchTerm} />
            </div>
          </div>
        </header>

        <main className="p-4 pt-6">
          {processedPosts.length > 0 ? (
            <div className="w-full max-w-[2000px] mx-auto">
              <div className="columns-1 sm:columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-4">
                {/* Render all posts as individual cards */}
                {processedPosts.map(post => (
                  <div key={post.id} className="break-inside-avoid mb-4 inline-block w-full">
                    <PostCard 
                      post={post} 
                      onDelete={handleDeletePost} 
                      onOpenDetails={handleOpenPostDetails} // Pass the open details handler
                    />
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="p-8 text-center text-gray-500">
              No saved posts found{/* TODO: Add helpful message if filters are active */}
            </div>
          )}
        </main>

        <LoginModal 
          isOpen={isLoginModalOpen} 
          onClose={handleCloseLoginModal} 
          onLoginSuccess={handleLoginSuccess}
          preventAutoGoogleLogin={true}
        />

        {/* --- NEW: Render PostViewerFullScreen --- */}
        {selectedPostForViewer && (
          <PostViewerFullScreen
            post={selectedPostForViewer}
            onClose={handleClosePostViewer}
            onAddCategory={handleAddCategoryToPost}
            onRemoveTag={handleRemoveTagFromPost}
            onRemoveCategory={handleRemoveCategoryFromPost}
            // Pass other necessary handlers if PostViewerFullScreen requires them
          />
        )}
        {/* --- END Render PostViewerFullScreen --- */}

      </div>
    </div>
  );
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Dashboard />
  </React.StrictMode>,
);
