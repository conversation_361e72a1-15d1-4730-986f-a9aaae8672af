/**
 * Authentication Service
 *
 * This service handles authentication with the backend API.
 * It provides methods for getting tokens, checking authentication status,
 * and handling login/logout.
 */

// Simple token storage in Chrome extension storage
export const getToken = async (): Promise<string | null> => {
  try {
    const result = await chrome.storage.local.get(['authToken']);
    return result.authToken || null;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Check if the user is authenticated
export const isAuthenticated = async (): Promise<boolean> => {
  const token = await getToken();
  return !!token;
};

// Trigger the authentication flow
export const login = async (): Promise<void> => {
  try {
    // Send message to background script to handle authentication
    await chrome.runtime.sendMessage({ action: 'LOGIN' });
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

// Log out the user
export const logout = async (): Promise<void> => {
  try {
    // Clear the token from storage
    await chrome.storage.local.remove(['authToken', 'auth_timestamp']);
    // Send message to background script to handle any additional logout tasks
    await chrome.runtime.sendMessage({ action: 'LOGOUT' });
  } catch (error) {
    console.error('Logout error:', error);
    throw error;
  }
};

// Get the user profile
export const getUser = async (): Promise<any> => {
  try {
    const result = await chrome.storage.local.get(['user_profile']);
    return result.user_profile || null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
};

// Get user plan (free/premium)
export const getUserPlan = async (): Promise<'free' | 'premium' | null> => {
  try {
    const user = await getUser();
    console.log('getUserPlan: user object:', user);
    if (!user) {
      console.log('getUserPlan: No user found, returning null');
      return null;
    }
    const plan = user.plan || 'free';
    console.log('getUserPlan: returning plan:', plan);
    return plan;
  } catch (error) {
    console.error('Error getting user plan:', error);
    return null;
  }
};

// Check if user is premium
export const isPremiumUser = async (): Promise<boolean> => {
  try {
    const plan = await getUserPlan();
    return plan === 'premium';
  } catch (error) {
    console.error('Error checking premium status:', error);
    return false;
  }
};

// Initialize authentication
export const initAuth = async (): Promise<void> => {
  // Nothing to do here for now
  // This could check token validity, refresh tokens, etc.
};

// Diagnose authentication issues
export const diagnoseAuthIssues = async (): Promise<any> => {
  try {
    const authData = await chrome.storage.local.get([
      'auth_token',
      'auth_timestamp',
      'auth_error',
      'user_profile'
    ]);

    return {
      hasToken: !!authData.auth_token,
      tokenTimestamp: authData.auth_timestamp || null,
      tokenAge: authData.auth_timestamp
        ? Math.floor((Date.now() - authData.auth_timestamp) / 1000 / 60) + ' minutes'
        : 'N/A',
      lastError: authData.auth_error || null,
      hasUserProfile: !!authData.user_profile
    };
  } catch (error) {
    console.error('Error diagnosing auth issues:', error);
    return {
      error: error instanceof Error ? error.message : 'Unknown error',
      hasToken: false,
      tokenTimestamp: null,
      tokenAge: 'N/A',
      lastError: 'Error retrieving auth data',
      hasUserProfile: false
    };
  }
};
