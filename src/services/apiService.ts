import * as authService from './authService';

const API_URL = 'https://api.notely.social';

/**
 * Make an authenticated API request
 */
export const fetchWithAuth = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<any> => {
  try {
    const token = await authService.getToken();

    if (!token) {
      throw new Error('No authentication token available');
    }

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers
    };

    const response = await fetch(`${API_URL}${endpoint}`, {
      ...options,
      headers
    });

    // Handle token expiration
    if (response.status === 401) {
      // Try to refresh token or redirect to login
      // For now, just throw an error
      throw new Error('Authentication token expired');
    }

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

/**
 * Get user profile from the API
 */
export const getUserProfile = async (): Promise<any> => {
  return fetchWithAuth('/user/profile');
};

/**
 * Update user profile
 */
export const updateUserProfile = async (profileData: any): Promise<any> => {
  return fetchWithAuth('/user/profile', {
    method: 'PUT',
    body: JSON.stringify(profileData)
  });
};

/**
 * Get user subscription info
 */
export const getUserSubscription = async (): Promise<any> => {
  return fetchWithAuth('/user/subscription');
};