import { v4 as uuidv4 } from 'uuid';
import type { 
  WisdomQuote, 
  UserWisdomPreferences, 
  WisdomInsight, 
  WisdomStorage 
} from '../types/wisdom';

const WISDOM_STORAGE_KEY = 'wisdomStorage';

// Default storage structure
const DEFAULT_WISDOM_STORAGE: WisdomStorage = {
  quotes: [],
  preferences: {
    lastShownDate: undefined,
    preferredCategories: [],
    preferredAuthors: <AUTHORS>
  },
  insights: {
    topCategories: [],
    commonTags: [],
    lastAnalyzedAt: new Date(0).toISOString()
  }
};

export const getWisdomStorage = async (): Promise<WisdomStorage> => {
  try {
    console.log('wisdomService: Getting storage for key:', WISDOM_STORAGE_KEY);
    const result = await chrome.storage.local.get(WISDOM_STORAGE_KEY);
    console.log('wisdomService: Raw storage result:', result);
    
    if (!result[WISDOM_STORAGE_KEY]) {
      console.log('wisdomService: No existing storage found, returning default storage');
      return { ...DEFAULT_WISDOM_STORAGE };
    }
    
    console.log('wisdomService: Found existing storage, returning it');
    return result[WISDOM_STORAGE_KEY];
  } catch (error) {
    console.error('wisdomService: Error getting storage:', error);
    // Return default storage on error
    return { ...DEFAULT_WISDOM_STORAGE };
  }
};

export const saveWisdomStorage = async (storage: Partial<WisdomStorage>): Promise<void> => {
  console.log('wisdomService: saveWisdomStorage called with storage update:', storage);
  
  try {
    console.log('wisdomService: Getting current storage for merge...');
    const currentStorage = await getWisdomStorage();
    console.log('wisdomService: Current storage before update:', currentStorage);
    
    const updatedStorage = {
      ...currentStorage,
      ...storage,
      // Ensure we don't lose any existing data when updating partial storage
      preferences: {
        ...currentStorage.preferences,
        ...(storage.preferences || {})
      },
      insights: {
        ...currentStorage.insights,
        ...(storage.insights || {})
      }
    };
    
    console.log('wisdomService: Saving updated storage:', updatedStorage);
    
    await chrome.storage.local.set({
      [WISDOM_STORAGE_KEY]: updatedStorage
    });
    
    console.log('wisdomService: Storage saved successfully');
    
    // Verify the save
    const verifyStorage = await chrome.storage.local.get(WISDOM_STORAGE_KEY);
    const savedData = verifyStorage[WISDOM_STORAGE_KEY];
    console.log('wisdomService: Verified storage after save:', savedData);
    
    if (!savedData) {
      console.error('wisdomService: Failed to verify storage after save');
    }
  } catch (error) {
    console.error('wisdomService: Error saving to storage:', error);
    throw error;
  }
};

// Quote Operations
export const addWisdomQuote = async (quote: Omit<WisdomQuote, 'id' | 'createdAt'>): Promise<WisdomQuote> => {
  console.log('wisdomService: addWisdomQuote called with quote:', quote);
  
  const newQuote: WisdomQuote = {
    ...quote,
    id: uuidv4(),
    createdAt: new Date().toISOString(),
  };
  
  console.log('wisdomService: Created new quote object:', newQuote);
  
  console.log('wisdomService: Getting current storage...');
  const { quotes } = await getWisdomStorage();
  console.log(`wisdomService: Current storage has ${quotes.length} quotes`);
  
  const updatedQuotes = [...quotes, newQuote];
  console.log('wisdomService: Saving updated quotes to storage...');
  
  await saveWisdomStorage({
    quotes: updatedQuotes
  });
  
  // Verify the quote was saved
  const storageAfterSave = await getWisdomStorage();
  const savedQuote = storageAfterSave.quotes.find(q => q.id === newQuote.id);
  
  if (savedQuote) {
    console.log('wisdomService: Successfully saved quote with ID:', savedQuote.id);
  } else {
    console.error('wisdomService: Failed to verify quote was saved');
  }
  
  return newQuote;
};

export const getWisdomQuote = async (id: string): Promise<WisdomQuote | undefined> => {
  const { quotes } = await getWisdomStorage();
  return quotes.find(quote => quote.id === id);
};

export const updateWisdomQuote = async (id: string, updates: Partial<Omit<WisdomQuote, 'id' | 'createdAt'>>): Promise<WisdomQuote | null> => {
  const { quotes } = await getWisdomStorage();
  const quoteIndex = quotes.findIndex(q => q.id === id);
  
  if (quoteIndex === -1) return null;
  
  const updatedQuote = { 
    ...quotes[quoteIndex], 
    ...updates,
    // Don't allow updating these fields through this method
    id: quotes[quoteIndex].id,
    createdAt: quotes[quoteIndex].createdAt
  };
  
  const updatedQuotes = [...quotes];
  updatedQuotes[quoteIndex] = updatedQuote;
  
  await saveWisdomStorage({ quotes: updatedQuotes });
  return updatedQuote;
};

export const deleteWisdomQuote = async (id: string): Promise<boolean> => {
  const { quotes } = await getWisdomStorage();
  const newQuotes = quotes.filter(quote => quote.id !== id);
  
  if (newQuotes.length === quotes.length) return false;
  
  await saveWisdomStorage({ quotes: newQuotes });
  return true;
};

export const getAllWisdomQuotes = async (): Promise<WisdomQuote[]> => {
  const { quotes } = await getWisdomStorage();
  return [...quotes].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
};

// Preferences Operations
export const getUserWisdomPreferences = async (): Promise<UserWisdomPreferences> => {
  const { preferences } = await getWisdomStorage();
  return { ...preferences };
};

export const updateUserWisdomPreferences = async (
  updates: Partial<UserWisdomPreferences>
): Promise<UserWisdomPreferences> => {
  const currentStorage = await getWisdomStorage();
  const updatedPreferences = {
    ...currentStorage.preferences,
    ...updates
  };
  
  await saveWisdomStorage({ preferences: updatedPreferences });
  return updatedPreferences;
};

// Insights Operations
export const getWisdomInsights = async (): Promise<WisdomInsight> => {
  const { insights } = await getWisdomStorage();
  return { ...insights };
};

export const updateWisdomInsights = async (
  updates: Partial<WisdomInsight>
): Promise<WisdomInsight> => {
  const currentStorage = await getWisdomStorage();
  const updatedInsights = {
    ...currentStorage.insights,
    ...updates,
    lastAnalyzedAt: new Date().toISOString()
  };
  
  await saveWisdomStorage({ insights: updatedInsights });
  return updatedInsights;
};

// Daily Wisdom Feature
export const getTodaysWisdom = async (): Promise<WisdomQuote | null> => {
  console.log('wisdomService: getTodaysWisdom called');
  try {
    // Ensure storage is initialized
    console.log('wisdomService: Ensuring storage is initialized...');
    await ensureStorageInitialized();
    
    const today = new Date().toISOString().split('T')[0];
    console.log('wisdomService: Today\'s date:', today);
    
    const storage = await getWisdomStorage();
    console.log('wisdomService: Retrieved storage:', storage);
    
    const { preferences, quotes } = storage;
    console.log(`wisdomService: Found ${quotes.length} quotes in storage`);
    
    // Check if we've already shown a quote today
    if (preferences.lastShownDate === today && quotes.length > 0) {
      console.log('wisdomService: Found quote shown today, returning it');
      // Return the most recently shown quote
      const todaysQuote = quotes.find(q => q.shownAt?.startsWith(today));
      if (todaysQuote) {
        console.log('wisdomService: Returning today\'s quote:', todaysQuote);
        return todaysQuote;
      }
    }
    
    if (quotes.length === 0) {
      console.log('wisdomService: No wisdom quotes available');
      return null;
    }
    
    // Simple random selection - can be enhanced with more sophisticated logic
    const randomIndex = Math.floor(Math.random() * quotes.length);
    console.log('wisdomService: Selected random quote at index:', randomIndex);
    
    const todaysQuote = { 
      ...quotes[randomIndex], 
      shownAt: new Date().toISOString() 
    };
    
    console.log('wisdomService: Updating quote with shownAt timestamp...');
    // Update the quote with shownAt timestamp
    const updatedQuote = await updateWisdomQuote(todaysQuote.id, { shownAt: todaysQuote.shownAt });
    
    // Update last shown date
    console.log('wisdomService: Updating last shown date...');
    await updateUserWisdomPreferences({ lastShownDate: today });
    
    console.log('wisdomService: Selected new quote for today:', updatedQuote);
    return updatedQuote || null;
  } catch (error) {
    console.error('wisdomService: Error in getTodaysWisdom:', error);
    return null;
  }
};

// Initialize storage if not exists
export const initializeStorage = async (): Promise<void> => {
  console.log('wisdomService: initializeStorage called');
  try {
    console.log('wisdomService: Getting current storage...');
    const currentStorage = await getWisdomStorage();
    console.log('wisdomService: Current storage:', currentStorage);
    
    // If no quotes exist, initialize with some default quotes
    if (currentStorage.quotes.length === 0) {
      console.log('wisdomService: Initializing default wisdom quotes...');
      
      const defaultQuotes: Omit<WisdomQuote, 'id' | 'createdAt'>[] = [
        {
          text: "The only way to do great work is to love what you do.",
          author: "Steve Jobs",
          categories: ["Motivation", "Work"],
          tags: ["inspiration", "career"],
          source: "Stanford Commencement Speech 2005"
        },
        {
          text: "Innovation distinguishes between a leader and a follower.",
          author: "Steve Jobs",
          categories: ["Leadership", "Innovation"],
          tags: ["business", "strategy"]
        },
        {
          text: "Your time is limited, don't waste it living someone else's life.",
          author: "Steve Jobs",
          categories: ["Life", "Motivation"],
          tags: ["wisdom", "self-improvement"]
        }
      ];

      // Add default quotes
      console.log('wisdomService: Adding default quotes...');
      for (const quote of defaultQuotes) {
        console.log('wisdomService: Adding quote:', quote.text);
        const addedQuote = await addWisdomQuote(quote);
        console.log('wisdomService: Added quote with ID:', addedQuote.id);
      }
      
      // Verify quotes were added
      const updatedStorage = await getWisdomStorage();
      console.log(`wisdomService: Storage now has ${updatedStorage.quotes.length} quotes`);
      console.log('wisdomService: Default wisdom quotes initialized');
    } else {
      console.log('wisdomService: Storage already has quotes, skipping initialization');
    }
  } catch (error) {
    console.error('wisdomService: Failed to initialize wisdom storage:', error);
    throw error;
  }
};

// Function to ensure storage is initialized
export const ensureStorageInitialized = async (): Promise<void> => {
  try {
    await initializeStorage();
  } catch (error) {
    console.error('Storage initialization failed:', error);
  }
};

// Initialize on import
ensureStorageInitialized().catch(console.error);

// Function to add a sample quote (for testing)
export const addSampleQuote = async (): Promise<WisdomQuote> => {
  const sampleQuote: Omit<WisdomQuote, 'id' | 'createdAt'> = {
    text: "This is a sample quote to test the Daily Wisdom feature.",
    author: "System",
    categories: ["Test", "Sample"],
    tags: ["test", "sample"],
    source: "System Generated"
  };
  return addWisdomQuote(sampleQuote);
};
