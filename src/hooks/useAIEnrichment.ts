import { useState, useCallback } from 'react';
import { generateAICategories, generateAITags, generateAIInsight, generateAIFastTake, generateAISnapNote } from '../services/aiService';
import { canMakeAICall, incrementAIUsage } from '../services/usageTrackingService';
import { CORE_CATEGORIES } from '../config/constants';
import type { CoreSubCategorySlug } from '../config/constants';

export interface AIEnrichmentResult {
  aiCategory: string | null;
  aiTags: string[];
  userCategory: string;
  userTags: string[];
  snapNote?: string | null;
  insight?: {
    sentiment: 'positive' | 'neutral' | 'negative';
    emoji: string;
    contextTags: string[];
  } | null;
  fastTake?: string | null;
}

export interface AIEnrichmentState {
  isLoading: boolean;
  error: string | null;
  result: AIEnrichmentResult | null;
  canUseAI: boolean;
  usageInfo: {
    canCall: boolean;
    reason?: string;
    remainingCalls?: number;
  } | null;
}

/**
 * Hook for managing AI enrichment of posts
 */
export const useAIEnrichment = (userPlan: 'free' | 'premium' | null) => {
  const [state, setState] = useState<AIEnrichmentState>({
    isLoading: false,
    error: null,
    result: null,
    canUseAI: false,
    usageInfo: null
  });

  /**
   * Check if user can use AI features
   */
  const checkAIAvailability = useCallback(async () => {
    console.log('useAIEnrichment: checkAIAvailability called with userPlan:', userPlan);
    const usageInfo = await canMakeAICall(userPlan);
    console.log('useAIEnrichment: canMakeAICall returned:', usageInfo);
    setState(prev => ({
      ...prev,
      canUseAI: usageInfo.canCall,
      usageInfo
    }));
    return usageInfo;
  }, [userPlan]);

  /**
   * Find matching category from predefined categories
   */
  const findMatchingCategory = (aiSuggestedCategory: string): string | null => {
    const allCategories = Object.values(CORE_CATEGORIES).flat();

    // Exact match first
    const exactMatch = allCategories.find(cat =>
      cat.toLowerCase() === aiSuggestedCategory.toLowerCase()
    );
    if (exactMatch) return exactMatch;

    // Fuzzy match - check if AI suggestion contains or is contained in any category
    const fuzzyMatch = allCategories.find(cat => {
      const catLower = cat.toLowerCase().replace(/_/g, ' ');
      const aiLower = aiSuggestedCategory.toLowerCase().replace(/_/g, ' ');
      return catLower.includes(aiLower) || aiLower.includes(catLower);
    });

    return fuzzyMatch || null;
  };

  /**
   * Enrich post content with AI
   */
  const enrichPost = useCallback(async (postContent: string, imageUrl?: string): Promise<AIEnrichmentResult | null> => {
    console.log('useAIEnrichment: enrichPost called with userPlan:', userPlan);
    console.log('useAIEnrichment: postContent length:', postContent.length);
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Check if user can make AI calls
      const usageCheck = await canMakeAICall(userPlan);
      if (!usageCheck.canCall) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: usageCheck.reason || 'Cannot make AI call',
          usageInfo: usageCheck
        }));
        return null;
      }

      // Increment usage counter (only for non-premium users)
      if (userPlan !== 'premium') {
        console.log('useAIEnrichment: Incrementing usage for non-premium user');
        await incrementAIUsage();
      } else {
        console.log('useAIEnrichment: Skipping usage increment for premium user');
      }

      // Make AI calls in parallel for better performance
      const [categoriesResult, tagsResult, insightResult, fastTakeResult, snapNoteResult] = await Promise.allSettled([
        generateAICategories(postContent),
        generateAITags(postContent),
        generateAIInsight(postContent),
        generateAIFastTake(postContent),
        generateAISnapNote(postContent, imageUrl)
      ]);

      // Process categories
      let aiCategory: string | null = null;
      if (categoriesResult.status === 'fulfilled' && categoriesResult.value.length > 0) {
        const suggestedCategory = categoriesResult.value[0];
        aiCategory = findMatchingCategory(suggestedCategory) || suggestedCategory;
      }

      // Process tags
      const aiTags = tagsResult.status === 'fulfilled' ? tagsResult.value : [];

      // Process insight
      const insight = insightResult.status === 'fulfilled' ? insightResult.value : null;

      // Process fast take
      const fastTake = fastTakeResult.status === 'fulfilled' ? fastTakeResult.value : null;

      // Process snap note
      const snapNote = snapNoteResult.status === 'fulfilled' ? snapNoteResult.value : null;

      const result: AIEnrichmentResult = {
        aiCategory,
        aiTags,
        userCategory: aiCategory || '', // Initialize with AI suggestion
        userTags: [...aiTags], // Initialize with AI suggestions
        snapNote,
        insight,
        fastTake
      };

      setState(prev => ({
        ...prev,
        isLoading: false,
        result,
        usageInfo: usageCheck
      }));

      return result;

    } catch (error) {
      console.error('AI enrichment error:', error);
      const errorMessage = error instanceof Error ? error.message : 'AI enrichment failed';

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));

      return null;
    }
  }, [userPlan]);

  /**
   * Update user-edited category
   */
  const updateUserCategory = useCallback((category: string) => {
    setState(prev => ({
      ...prev,
      result: prev.result ? {
        ...prev.result,
        userCategory: category
      } : null
    }));
  }, []);

  /**
   * Update user-edited tags
   */
  const updateUserTags = useCallback((tags: string[]) => {
    setState(prev => ({
      ...prev,
      result: prev.result ? {
        ...prev.result,
        userTags: tags
      } : null
    }));
  }, []);

  /**
   * Add a user tag
   */
  const addUserTag = useCallback((tag: string) => {
    setState(prev => ({
      ...prev,
      result: prev.result ? {
        ...prev.result,
        userTags: [...new Set([...prev.result.userTags, tag])]
      } : null
    }));
  }, []);

  /**
   * Remove a user tag
   */
  const removeUserTag = useCallback((tag: string) => {
    setState(prev => ({
      ...prev,
      result: prev.result ? {
        ...prev.result,
        userTags: prev.result.userTags.filter(t => t !== tag)
      } : null
    }));
  }, []);

  /**
   * Clear all AI data
   */
  const clearAIData = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      result: null,
      canUseAI: false,
      usageInfo: null
    });
  }, []);

  return {
    ...state,
    enrichPost,
    updateUserCategory,
    updateUserTags,
    addUserTag,
    removeUserTag,
    clearAIData,
    checkAIAvailability
  };
};
