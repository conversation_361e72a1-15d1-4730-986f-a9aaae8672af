/* Notely Dark Mode-First Theme */

/* Use system fonts for better performance and compatibility */
/* Removed external Google Fonts imports to fix MIME type issues in Chrome extensions */

/* Root variables for theme switching */
:root {
  /* Dark theme (default) */
  --notely-bg: #0f0f0f;
  --notely-surface: #1a1a1a;
  --notely-card: #252525;
  --notely-border: #333333;
  --notely-text-primary: #ffffff;
  --notely-text-secondary: #b3b3b3;
  --notely-text-muted: #666666;

  /* Accent colors */
  --notely-coral: #ff6b6b;
  --notely-mint: #51cf66;
  --notely-sky: #74c0fc;
  --notely-lavender: #b197fc;
  --notely-peach: #ffa8a8;
  --notely-sage: #8ce99a;

  /* Spacing */
  --notely-space-xs: 8px;
  --notely-space-sm: 12px;
  --notely-space-md: 16px;
  --notely-space-lg: 24px;
  --notely-space-xl: 32px;
  --notely-space-2xl: 48px;

  /* Border radius */
  --notely-radius-sm: 8px;
  --notely-radius-md: 12px;
  --notely-radius-lg: 16px;
  --notely-radius-xl: 20px;
  --notely-radius-2xl: 24px;

  /* Shadows */
  --notely-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --notely-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --notely-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --notely-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.4);
}

/* Light theme override */
.light-theme {
  --notely-bg: #fafbfc;
  --notely-surface: #ffffff;
  --notely-card: #ffffff;
  --notely-border: #e1e5e9;
  --notely-text-primary: #1a202c;
  --notely-text-secondary: #4a5568;
  --notely-text-muted: #718096;

  /* Lighter shadows for light theme */
  --notely-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --notely-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --notely-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --notely-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Logo visibility based on theme */
.notely-logo-light {
  display: none;
}

.notely-logo-dark {
  display: block;
}

.light-theme .notely-logo-light {
  display: block;
}

.light-theme .notely-logo-dark {
  display: none;
}

/* Search input styling */
.notely-search-input {
  background-color: var(--notely-surface);
  border-color: var(--notely-border);
  color: var(--notely-text-primary);
  box-shadow: var(--notely-shadow-sm);
}

.notely-search-input:focus {
  border-color: var(--notely-sky);
  box-shadow: 0 0 0 3px rgba(116, 192, 252, 0.1);
}

.notely-search-input::placeholder {
  color: var(--notely-text-muted);
}

.light-theme .notely-search-input {
  background-color: #ffffff;
  border-color: #d1d5db;
  color: var(--notely-text-primary);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.light-theme .notely-search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.light-theme .notely-search-input::placeholder {
  color: #9ca3af;
}

/* Base styles */
body {
  background-color: var(--notely-bg);
  color: var(--notely-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  transition: background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1), color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure all elements inherit the theme */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Typography classes */
.notely-heading {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-weight: 600;
  color: var(--notely-text-primary);
}

.notely-body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  color: var(--notely-text-secondary);
}

.notely-quote {
  font-family: 'Georgia', serif;
  font-style: italic;
  color: var(--notely-text-secondary);
}

/* Card styles */
.notely-card {
  background-color: var(--notely-card);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-lg);
  box-shadow: var(--notely-shadow-md);
  transition: all 0.3s ease;
}

.notely-card:hover {
  box-shadow: var(--notely-shadow-lg);
  transform: translateY(-2px);
}

/* Surface styles */
.notely-surface {
  background-color: var(--notely-surface);
  border-radius: var(--notely-radius-md);
}

/* Button styles */
.notely-btn {
  border-radius: var(--notely-radius-md);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.notely-btn-primary {
  background-color: var(--notely-sky);
  color: white;
  padding: var(--notely-space-sm) var(--notely-space-lg);
}

.notely-btn-primary:hover {
  background-color: #5ba7f7;
  transform: translateY(-1px);
  box-shadow: var(--notely-shadow-md);
}

/* Pill button styles for platform filters */
.notely-pill {
  border-radius: 9999px;
  padding: var(--notely-space-xs) var(--notely-space-md);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid var(--notely-border);
  background-color: var(--notely-surface);
  color: var(--notely-text-secondary);
}

.notely-pill:hover {
  background-color: var(--notely-card);
  transform: translateY(-1px);
}

.notely-pill.active {
  color: white;
  border-color: transparent;
}

/* Platform-specific pill colors */
.notely-pill.active.twitter {
  background-color: #000000;
}

.notely-pill.active.linkedin {
  background-color: #0077b5;
}

.notely-pill.active.reddit {
  background-color: #FF4500;
}

.notely-pill.active.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.notely-pill.active.pinterest {
  background-color: #E60023;
}

.notely-pill.active.web {
  background: linear-gradient(45deg, var(--notely-mint), var(--notely-sky));
}

.notely-pill.active.all {
  background: linear-gradient(45deg, var(--notely-lavender), var(--notely-sky));
}

.notely-pill.active.mindstream {
  background: linear-gradient(45deg, #8b5cf6, #3b82f6, #6366f1);
}

/* Smooth transitions for category changes */
.notely-transition {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Progress bar styles */
.notely-progress {
  background-color: var(--notely-surface);
  border-radius: var(--notely-radius-sm);
  overflow: hidden;
  height: 4px;
}

.notely-progress-bar {
  height: 100%;
  border-radius: var(--notely-radius-sm);
  transition: width 0.3s ease;
}

/* Quick action button styles */
.notely-quick-action {
  background-color: var(--notely-surface);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-sm);
  padding: var(--notely-space-xs);
  color: var(--notely-text-muted);
  transition: all 0.2s ease;
  opacity: 0;
  transform: translateY(4px);
}

.notely-card:hover .notely-quick-action {
  opacity: 1;
  transform: translateY(0);
}

.notely-quick-action:hover {
  background-color: var(--notely-card);
  color: var(--notely-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--notely-shadow-sm);
}

/* Scrollbar styling for dark theme */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--notely-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--notely-border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--notely-text-muted);
}

/* Loading animation */
.notely-loading {
  animation: notely-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes notely-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Enhanced breathing room utility classes */
.notely-breathing-sm {
  padding: var(--notely-space-sm);
}

.notely-breathing-md {
  padding: var(--notely-space-md);
}

.notely-breathing-lg {
  padding: var(--notely-space-lg);
}

.notely-breathing-xl {
  padding: var(--notely-space-xl);
}

.notely-breathing-2xl {
  padding: var(--notely-space-2xl);
}

/* Post card specific breathing room */
.notely-post-content {
  padding: var(--notely-space-xl);
  margin: var(--notely-space-md) 0;
}

.notely-post-header {
  padding: var(--notely-space-lg) var(--notely-space-xl) var(--notely-space-md) var(--notely-space-xl);
}

.notely-post-footer {
  padding: var(--notely-space-md) var(--notely-space-xl) var(--notely-space-xl) var(--notely-space-xl);
}

/* Enhanced filter transition effects */
.notely-filter-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.notely-filter-enter {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.notely-filter-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.notely-filter-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.notely-filter-exit-active {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

/* Modal and popup animations */
.notely-modal-overlay {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-modal-content {
  transform: scale(0.95) translateY(20px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-modal-content.open {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* Stagger animation for post cards */
.notely-stagger-item {
  animation: notelyFadeInUp 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: translateY(15px);
}

.notely-stagger-item:nth-child(1) { animation-delay: 0.1s; }
.notely-stagger-item:nth-child(2) { animation-delay: 0.2s; }
.notely-stagger-item:nth-child(3) { animation-delay: 0.3s; }
.notely-stagger-item:nth-child(4) { animation-delay: 0.4s; }
.notely-stagger-item:nth-child(5) { animation-delay: 0.5s; }
.notely-stagger-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes notelyFadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Micro-interactions for category and tag buttons */
.notely-category-button {
  animation: notelySlideInFromLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateX(-20px);
}

.notely-tag-button {
  animation: notelySlideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateX(20px);
}

/* Stagger delays for category buttons */
.notely-category-button:nth-child(1) { animation-delay: 0.05s; }
.notely-category-button:nth-child(2) { animation-delay: 0.1s; }
.notely-category-button:nth-child(3) { animation-delay: 0.15s; }
.notely-category-button:nth-child(4) { animation-delay: 0.2s; }
.notely-category-button:nth-child(5) { animation-delay: 0.25s; }
.notely-category-button:nth-child(6) { animation-delay: 0.3s; }
.notely-category-button:nth-child(7) { animation-delay: 0.35s; }
.notely-category-button:nth-child(8) { animation-delay: 0.4s; }

/* Stagger delays for tag buttons */
.notely-tag-button:nth-child(1) { animation-delay: 0.1s; }
.notely-tag-button:nth-child(2) { animation-delay: 0.15s; }
.notely-tag-button:nth-child(3) { animation-delay: 0.2s; }
.notely-tag-button:nth-child(4) { animation-delay: 0.25s; }
.notely-tag-button:nth-child(5) { animation-delay: 0.3s; }
.notely-tag-button:nth-child(6) { animation-delay: 0.35s; }
.notely-tag-button:nth-child(7) { animation-delay: 0.4s; }
.notely-tag-button:nth-child(8) { animation-delay: 0.45s; }

@keyframes notelySlideInFromLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes notelySlideInFromRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse animation for active selections */
@keyframes notelyPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.notely-selection-pulse {
  animation: notelyPulse 0.6s ease-in-out;
}

/* Enhanced button styles */
.notely-btn-primary {
  background: linear-gradient(135deg, var(--notely-accent), var(--notely-accent-secondary));
  color: white;
  border: none;
  border-radius: var(--notely-radius-lg);
  padding: var(--notely-space-md) var(--notely-space-lg);
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--notely-shadow-md);
}

.notely-btn-primary:hover {
  box-shadow: var(--notely-shadow-lg);
  transform: translateY(-2px);
}

.notely-btn-secondary {
  background: var(--notely-surface);
  color: var(--notely-text-primary);
  border: 1px solid var(--notely-border);
}

/* Elegant Reading Mode Styles */
.notely-elegant-reading-card {
  /* Enhanced card styling for text-only posts */
  background: linear-gradient(135deg, var(--notely-card) 0%, var(--notely-surface) 100%);
  border: 2px solid var(--notely-border);
  box-shadow: var(--notely-shadow-lg);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-elegant-reading-card:hover {
  box-shadow: var(--notely-shadow-xl);
  transform: translateY(-4px);
  border-color: var(--notely-sky);
}

.notely-elegant-content {
  /* Georgia serif font for elegant reading */
  font-family: 'Georgia', 'Times New Roman', serif !important;
  font-size: 1.125rem; /* 18px */
  line-height: 1.75; /* 28px */
  letter-spacing: 0.025em;
  text-align: left;
  max-width: none;
  margin: 0 auto;
  position: relative;
}

/* Light theme adjustments for elegant reading */
.light-theme .notely-elegant-reading-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-color: #e2e8f0;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.light-theme .notely-elegant-reading-card:hover {
  box-shadow: 0 20px 35px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.light-theme .notely-elegant-content {
  color: #1e293b;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border-color: rgba(226, 232, 240, 0.5);
}

.notely-btn-secondary:hover {
  background: var(--notely-card);
  border-color: var(--notely-accent);
  transform: translateY(-1px);
}

.notely-btn-accent {
  background: linear-gradient(135deg, var(--notely-accent-light), var(--notely-accent));
  color: white;
  border: none;
  border-radius: var(--notely-radius-lg);
  padding: var(--notely-space-sm) var(--notely-space-md);
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-btn-accent:hover {
  transform: translateY(-1px);
  box-shadow: var(--notely-shadow-md);
}

.notely-btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  border-radius: var(--notely-radius-lg);
  padding: var(--notely-space-md) var(--notely-space-lg);
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-btn-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: var(--notely-shadow-md);
}

/* Quick action button styles */
.notely-quick-action {
  padding: var(--notely-space-sm);
  border-radius: var(--notely-radius-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: transparent;
  border: none;
}

.notely-quick-action:hover {
  background: var(--notely-surface);
  transform: scale(1.1);
}

/* Pill button styles */
.notely-pill {
  border-radius: var(--notely-radius-full);
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

.notely-pill.active {
  font-weight: 600;
  box-shadow: var(--notely-shadow-md);
}

/* Ask My Bookmarks Search Styles */
.notely-search-container {
  position: relative;
}

.notely-search-input {
  background: var(--notely-surface);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-search-input:focus-within {
  border-color: var(--notely-sky);
  box-shadow: 0 0 0 3px rgba(116, 192, 252, 0.1);
  transform: translateY(-1px);
}

.notely-search-input:hover:not(:focus-within) {
  border-color: var(--notely-mint);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notely-search-suggestions {
  background: var(--notely-surface);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-lg);
  box-shadow: var(--notely-shadow-xl);
  backdrop-filter: blur(8px);
  animation: notelySlideDown 0.2s ease-out;
}

.notely-search-suggestion-item {
  transition: all 0.15s ease;
}

.notely-search-suggestion-item:hover {
  background: var(--notely-card);
  transform: translateX(4px);
}

.notely-search-loading-dots {
  display: flex;
  align-items: center;
  gap: 4px;
}

.notely-search-loading-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: notelyPulse 1.4s ease-in-out infinite both;
}

.notely-search-loading-dot:nth-child(1) { animation-delay: -0.32s; }
.notely-search-loading-dot:nth-child(2) { animation-delay: -0.16s; }
.notely-search-loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes notelySlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes notelyPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Search Results Summary Styles */
.notely-search-results {
  animation: notelyFadeInUp 0.4s ease-out;
}

.notely-search-result-card {
  background: var(--notely-surface);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-lg);
  transition: all 0.2s ease;
}

.notely-search-result-card:hover {
  border-color: var(--notely-sky);
  box-shadow: var(--notely-shadow-md);
  transform: translateY(-2px);
}

.notely-search-highlight {
  background: linear-gradient(120deg, var(--notely-mint) 0%, var(--notely-sky) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
}

/* Pastel highlight borders for search */
.notely-search-border-mint {
  border-color: var(--notely-mint);
  box-shadow: 0 0 0 1px var(--notely-mint), 0 0 20px rgba(81, 207, 102, 0.1);
}

.notely-search-border-coral {
  border-color: var(--notely-coral);
  box-shadow: 0 0 0 1px var(--notely-coral), 0 0 20px rgba(255, 107, 107, 0.1);
}

/* Search input styling */
.notely-search-input {
  background-color: var(--notely-surface);
  border-color: var(--notely-border);
  color: var(--notely-text-primary);
}

.notely-search-input::placeholder {
  color: var(--notely-text-muted);
}

.notely-search-input:focus {
  background-color: var(--notely-card);
  border-color: var(--notely-sky);
  box-shadow: 0 0 0 2px rgba(116, 192, 252, 0.2);
}

.notely-search-input:hover:not(:disabled) {
  background-color: var(--notely-card);
  border-color: var(--notely-text-muted);
}

.notely-search-border-sky {
  border-color: var(--notely-sky);
  box-shadow: 0 0 0 1px var(--notely-sky), 0 0 20px rgba(116, 192, 252, 0.1);
}

/* Keyboard shortcut styling */
.notely-kbd {
  background: var(--notely-card);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-sm);
  padding: 2px 6px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 11px;
  font-weight: 500;
  color: var(--notely-text-muted);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
