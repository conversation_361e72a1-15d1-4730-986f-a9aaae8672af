/**
 * Common Button Styles
 * 
 * This module provides standardized button styles for the "Save to Notely" button
 * across all social media platforms.
 */

/**
 * Get the standard button style for the "Save to Notely" button
 * @param platform The platform to get the style for (optional)
 * @returns CSS string for the button
 */
export function getStandardButtonStyle(platform?: string): string {
  // Base style used by all platforms
  const baseStyle = `
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: background-color 0.3s ease;
  `;
  
  // Platform-specific style adjustments
  switch (platform) {
    case 'X/Twitter':
      return baseStyle + `
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      `;
    case 'LinkedIn':
      return baseStyle + `
        font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", sans-serif;
      `;
    case 'Instagram':
      return baseStyle + `
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 9999;
      `;
    case 'Reddit':
      return baseStyle + `
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      `;
    case 'pinterest':
      return baseStyle + `
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 9999;
      `;
    default:
      return baseStyle;
  }
}

/**
 * Get the style for the button when it's in the "saving" state
 * @returns CSS string for the saving state
 */
export function getSavingButtonStyle(): string {
  return `
    background-color: #999999;
    cursor: default;
  `;
}

/**
 * Get the style for the button when it's in the "saved" state
 * @returns CSS string for the saved state
 */
export function getSavedButtonStyle(): string {
  return `
    background-color: #17bf63;
  `;
}

/**
 * Get the style for the button when it's in the "error" state
 * @returns CSS string for the error state
 */
export function getErrorButtonStyle(): string {
  return `
    background-color: #e0245e;
  `;
}

/**
 * Get the absolute positioning style for the button
 * @returns CSS string for absolute positioning
 */
export function getAbsoluteButtonStyle(): string {
  return `
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 9999;
  `;
}
