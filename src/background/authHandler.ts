// Authentication handler for background script
import { env } from '../config/env';

const SERVER_BASE_URL = env.SERVER_BASE_URL;

export function handleLogin() {
  console.log('[Background] Initiating Google OAuth login flow...');
  
  // Create the OAuth 2.0 URL with the required parameters
  const authUrl = new URL(`${SERVER_BASE_URL}/auth/google`);
  
  // Get the extension ID for the redirect URL
  const extensionId = chrome.runtime.id;
  console.log(`[Background] Extension ID: ${extensionId}`);
  
  // Open the auth window
  chrome.windows.create({
    url: authUrl.toString(),
    type: 'popup',
    width: 600,
    height: 700
  }, (window) => {
    if (chrome.runtime.lastError) {
      console.error('[Background] Error opening auth window:', chrome.runtime.lastError.message);
    } else if (window) {
      console.log(`[Background] Auth window opened with ID: ${window.id}`);
    }
  });
}

// Listen for messages from the auth callback page
chrome.runtime.onMessageExternal.addListener((message, sender, sendResponse) => {
  if (message.type === 'AUTH_CALLBACK' && message.token) {
    console.log('[Background] Received auth callback with token');
    // Store the token
    chrome.storage.local.set({
      'auth_token': message.token,
      'auth_timestamp': Date.now()
    }).then(() => {
      // Close the auth window if it's still open
      if (sender.tab?.windowId) {
        chrome.windows.remove(sender.tab.windowId);
      }
      // Notify any listeners that auth is complete
      chrome.runtime.sendMessage({
        type: 'AUTH_COMPLETED',
        success: true
      });
    });
  }
});
