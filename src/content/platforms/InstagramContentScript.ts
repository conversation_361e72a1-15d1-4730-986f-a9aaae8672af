/**
 * Instagram Content Script - Refactored
 * 
 * This script handles the extraction and saving of Instagram posts using the
 * unified BaseContentScript architecture. It contains only Instagram-specific logic.
 */

import { BaseContentScript, ContentScriptConfig } from '../shared/BaseContentScript';

export class InstagramContentScript extends BaseContentScript {
  constructor() {
    const config: ContentScriptConfig = {
      platform: 'Instagram',
      postSelectors: [
        'article',
        'div[role="main"] article',
        'main article',
        'section article'
      ],
      buttonConfig: {
        position: 'absolute',
        platform: 'Instagram'
      },
      saveOptions: {
        includeImages: true,
        useCloudService: true,
        maxRetries: 2,
        retryDelay: 1000
      },
      mutationConfig: {
        debounceDelay: 500,
        targetContainer: 'main[role="main"]'
      },
      debug: true
    };

    super(config);
  }

  /**
   * Override initialization to add delayed scan for Instagram's dynamic loading
   */
  initialize(): void {
    super.initialize();
    
    // Additional scan after delay to catch dynamically loaded content
    setTimeout(() => {
      this.log('Delayed scan for Instagram posts');
      this.scanForPosts();
    }, 2000);
  }

  /**
   * Validate if an Instagram post element should be processed
   */
  protected validatePost(post: HTMLElement): boolean {
    // Check if the element has meaningful content
    const hasContent = post.textContent && post.textContent.trim().length > 0;
    
    // Check if the element has Instagram-specific images
    const hasImages = post.querySelector('img[src*="cdninstagram.com"], img[src*="fbcdn.net"]') !== null;
    
    // Check if it has video content
    const hasVideo = post.querySelector('video') !== null;

    // Must have content, images, or video to be considered a valid post
    return hasContent || hasImages || hasVideo;
  }

  /**
   * Find the best insertion point for the save button in an Instagram post
   */
  protected findButtonInsertionPoint(post: HTMLElement): {
    container: HTMLElement;
    method: 'append' | 'prepend' | 'before' | 'after' | 'absolute';
  } {
    // Try to find the section element (common in Instagram posts)
    const section = post.querySelector('section') as HTMLElement;
    
    if (section) {
      return { container: section, method: 'append' };
    }

    // Fallback to absolute positioning on the post itself
    return { container: post, method: 'absolute' };
  }

  /**
   * Insert the button at the specified location
   */
  protected insertButton(
    button: HTMLButtonElement,
    insertionPoint: { container: HTMLElement; method: string },
    post: HTMLElement
  ): void {
    const { container, method } = insertionPoint;

    try {
      if (method === 'append' && container.tagName.toLowerCase() === 'section') {
        this.log('Adding save button to section element');
        container.appendChild(button);
      } else {
        // Use absolute positioning
        this.useAbsolutePositioning(button, post);
      }
    } catch (error) {
      console.error('[Notely] Error inserting button in Instagram post:', error);
      this.useAbsolutePositioning(button, post);
    }
  }

  /**
   * Use absolute positioning for the button
   */
  private useAbsolutePositioning(button: HTMLButtonElement, post: HTMLElement): void {
    this.log('Using absolute positioning for Instagram button');
    
    // Apply absolute positioning styles
    button.style.cssText += `
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 9999;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    `;

    // Make sure the post has relative positioning
    if (window.getComputedStyle(post).position === 'static') {
      post.style.position = 'relative';
    }

    post.appendChild(button);
  }

  /**
   * Override scanForPosts to handle Instagram's specific structure
   */
  protected scanForPosts(): void {
    try {
      // Find all posts on the page
      const posts = this.findPosts();
      this.log(`Found ${posts.length} Instagram posts`);

      // Debug: Log page structure if no posts found
      if (posts.length === 0) {
        this.debugPageStructure();
        this.tryAlternativeSelectors();
      } else {
        // Process each post
        let processedCount = 0;
        posts.forEach((post, index) => {
          if (this.shouldProcessPost(post)) {
            this.log(`Processing Instagram post ${index + 1}/${posts.length}`);
            this.processPost(post);
            processedCount++;
          }
        });

        if (processedCount > 0) {
          this.log(`Added save buttons to ${processedCount} new Instagram posts`);
        }
      }
    } catch (error) {
      console.error('[Notely] Error scanning for Instagram posts:', error);
    }
  }

  /**
   * Debug page structure when no posts are found
   */
  private debugPageStructure(): void {
    this.log('No posts found. Checking page structure...');
    this.log('Total articles on page:', document.querySelectorAll('article').length);
    this.log('Total divs on page:', document.querySelectorAll('div').length);
    this.log('Page title:', document.title);
  }

  /**
   * Try alternative selectors when standard ones don't work
   */
  private tryAlternativeSelectors(): void {
    const alternativeSelectors = [
      'div[role="main"] article',
      'main article',
      'section article',
      'div[data-testid*="post"]',
      'div[class*="post"]'
    ];

    alternativeSelectors.forEach(selector => {
      const altPosts = document.querySelectorAll(selector);
      if (altPosts.length > 0) {
        this.log(`Alternative selector "${selector}" found ${altPosts.length} elements`);
        
        // Process these alternative posts
        altPosts.forEach(post => {
          if (this.shouldProcessPost(post as HTMLElement)) {
            this.processPost(post as HTMLElement);
          }
        });
      }
    });
  }

  /**
   * Handle Instagram-specific URL changes
   */
  protected onUrlChange(newUrl: string): void {
    this.log(`Instagram URL changed to: ${newUrl}`);
    
    // Instagram uses client-side routing
    // Clear processed elements and re-scan after a delay
    setTimeout(() => {
      this.processedElements.clear();
      this.scanForPosts();
    }, 1000);
  }

  /**
   * Handle Instagram-specific container changes
   */
  protected onContainerChange(): void {
    this.log('Instagram container changed');
    
    // Re-scan for posts after container change
    setTimeout(() => {
      this.scanForPosts();
    }, 500);
  }

  /**
   * Override to add Instagram-specific error handling
   */
  protected async handleSaveButtonClick(post: HTMLElement, button: HTMLButtonElement): Promise<void> {
    // Show progress notification for Instagram (since it can take longer)
    const progress = this.showProgressNotification();
    
    try {
      const result = await super.handleSaveButtonClick(post, button);
      
      if (result) {
        progress.complete('Instagram post saved successfully!');
      } else {
        progress.error('Failed to save Instagram post');
      }
    } catch (error) {
      progress.error('Error saving Instagram post');
      throw error;
    }
  }

  /**
   * Show a progress notification for Instagram saves
   */
  private showProgressNotification() {
    // Import NotificationManager dynamically to avoid circular dependencies
    return {
      complete: (message: string) => {
        // This would use NotificationManager.showSuccess in a real implementation
        console.log(`[Notely Instagram] ${message}`);
      },
      error: (message: string) => {
        // This would use NotificationManager.showError in a real implementation
        console.error(`[Notely Instagram] ${message}`);
      }
    };
  }
}

// Initialize the Instagram content script
export const instagramScript = BaseContentScript.initializeWhenReady(InstagramContentScript);
