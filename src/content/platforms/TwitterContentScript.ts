/**
 * Twitter/X Content Script - Refactored
 * 
 * This script handles the extraction and saving of Twitter/X tweets using the
 * unified BaseContentScript architecture. It contains only Twitter-specific logic.
 */

import { BaseContentScript, ContentScriptConfig } from '../shared/BaseContentScript';

export class Twitter<PERSON>ontentScript extends BaseContentScript {
  private threadButtonsAdded = new Set<HTMLElement>();

  constructor() {
    const config: ContentScriptConfig = {
      platform: 'X/Twitter',
      postSelectors: [
        'article[data-testid="tweet"]',
        'div[data-testid="cellInnerDiv"] article',
        'div[data-testid="tweetDetail"] article',
        'div.tweet'
      ],
      buttonConfig: {
        position: 'inline',
        platform: 'X/Twitter'
      },
      saveOptions: {
        includeImages: true,
        useCloudService: true
      },
      mutationConfig: {
        debounceDelay: 300,
        targetContainer: '[data-testid="primaryColumn"]'
      },
      debug: true
    };

    super(config);
    this.injectThreadButtonStyles();
  }

  /**
   * Inject CSS styles for thread buttons
   */
  private injectThreadButtonStyles(): void {
    if (document.getElementById('notely-thread-styles')) return;

    const style = document.createElement('style');
    style.id = 'notely-thread-styles';
    style.textContent = `
      .notely-thread-save-btn {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .notely-thread-save-btn:hover {
        transform: translateY(-1px);
      }

      .notely-thread-save-btn:active {
        transform: translateY(0);
      }

      .notely-thread-save-btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none !important;
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * Validate if a tweet element should be processed
   */
  protected validatePost(tweet: HTMLElement): boolean {
    // Check if the element has content or images
    const hasContent = tweet.textContent && tweet.textContent.trim().length > 0;
    const hasImage = tweet.querySelector('img[src*="twimg.com"]') !== null;

    return hasContent || hasImage;
  }

  /**
   * Find the best insertion point for the save button in a tweet
   */
  protected findButtonInsertionPoint(tweet: HTMLElement): {
    container: HTMLElement;
    method: 'append' | 'prepend' | 'before' | 'after' | 'absolute';
  } {
    // Try to find the action bar (retweet, like buttons)
    const actionBar = tweet.querySelector('div[role="group"]') as HTMLElement;
    
    if (actionBar) {
      return { container: actionBar, method: 'append' };
    }

    // Fallback to absolute positioning
    return { container: tweet, method: 'absolute' };
  }

  /**
   * Insert the button at the specified location
   */
  protected insertButton(
    button: HTMLButtonElement,
    insertionPoint: { container: HTMLElement; method: string },
    tweet: HTMLElement
  ): void {
    const { container, method } = insertionPoint;

    try {
      if (method === 'append' && container.querySelector('div[role="group"]')) {
        // Create a container to match Twitter's style
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
          display: flex;
          align-items: center;
          margin-left: 8px;
        `;
        buttonContainer.appendChild(button);

        // Try to insert after the last action button
        const lastAction = Array.from(container.children).pop();
        if (lastAction) {
          container.insertBefore(buttonContainer, lastAction.nextSibling);
        } else {
          container.appendChild(buttonContainer);
        }

        this.log('Button added to tweet action bar');
      } else {
        // Use absolute positioning
        this.useAbsolutePositioning(button, tweet);
      }
    } catch (error) {
      console.error('[Notely] Error inserting button in tweet:', error);
      this.useAbsolutePositioning(button, tweet);
    }
  }

  /**
   * Use absolute positioning as fallback
   */
  private useAbsolutePositioning(button: HTMLButtonElement, tweet: HTMLElement): void {
    this.log('Using absolute positioning for button');
    
    // Apply absolute positioning styles
    button.style.cssText += `
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 9999;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    `;

    // Find a container element to append to
    const container = tweet.querySelector('[data-testid="tweet"], [data-testid="tweetDetail"], div[data-testid="cellInnerDiv"]') || tweet;

    // Make sure the container has relative positioning
    if (window.getComputedStyle(container).position === 'static') {
      (container as HTMLElement).style.position = 'relative';
    }

    container.appendChild(button);
  }

  /**
   * Override scanForPosts to handle Twitter's dynamic loading and thread detection
   */
  protected scanForPosts(): void {
    try {
      // Find all tweets on the page
      const tweets = this.findPosts();
      this.log(`Found ${tweets.length} tweets`);

      if (tweets.length === 0) {
        // If no tweets found with standard selectors, try fallback approach
        this.tryFallbackScan();
      } else {
        // Process each tweet found with standard selectors
        let processedCount = 0;
        tweets.forEach(tweet => {
          if (this.shouldProcessPost(tweet)) {
            this.processPost(tweet);
            processedCount++;
          }
        });

        // Check for threads and add thread save buttons
        this.detectAndProcessThreads();

        if (processedCount > 0) {
          this.log(`Added save buttons to ${processedCount} new tweets`);
        }
      }
    } catch (error) {
      console.error('[Notely] Error scanning for tweets:', error);
    }
  }

  /**
   * Fallback scan for when Twitter changes their DOM structure
   */
  private tryFallbackScan(): void {
    const possibleTweets = document.querySelectorAll('article');
    if (possibleTweets.length > 0) {
      this.log(`Found ${possibleTweets.length} possible tweets using fallback selector`);

      possibleTweets.forEach(tweet => {
        // Only process if it looks like a tweet
        if (this.validatePost(tweet as HTMLElement) && this.shouldProcessPost(tweet as HTMLElement)) {
          this.processPost(tweet as HTMLElement);
        }
      });
    }
  }

  /**
   * Handle Twitter-specific URL changes
   */
  protected onUrlChange(newUrl: string): void {
    this.log(`Twitter URL changed to: ${newUrl}`);
    
    // Twitter uses client-side routing, so we need to handle navigation
    // Clear processed elements and re-scan after a short delay
    setTimeout(() => {
      this.processedElements.clear();
      this.scanForPosts();
    }, 500);
  }

  /**
   * Handle Twitter-specific container changes
   */
  protected onContainerChange(): void {
    this.log('Twitter container changed');

    // Re-scan for tweets after container change
    setTimeout(() => {
      this.scanForPosts();
    }, 200);
  }

  /**
   * Detect threads and add thread save buttons
   */
  private detectAndProcessThreads(): void {
    try {
      // Look for thread indicators
      const threadContainers = this.findThreadContainers();

      threadContainers.forEach(container => {
        // Check if container already has a thread button
        const existingButton = container.querySelector('.notely-thread-save-btn');
        if (!existingButton && !this.threadButtonsAdded.has(container)) {
          this.addThreadSaveButton(container);
          this.threadButtonsAdded.add(container);
        }
      });
    } catch (error) {
      console.error('[Notely] Error detecting threads:', error);
    }
  }

  /**
   * Find containers that contain Twitter threads
   */
  private findThreadContainers(): HTMLElement[] {
    const containers: HTMLElement[] = [];
    const currentUrl = window.location.href;

    // Only detect threads on tweet detail pages or when explicit thread indicators are present
    if (currentUrl.includes('/status/')) {
      // We're on a tweet detail page, look for conversation threads
      const tweets = document.querySelectorAll('article[data-testid="tweet"]');
      if (tweets.length > 1) {
        // Check if multiple tweets are from the same author (thread)
        const authors = new Set();
        const tweetsByAuthor = new Map<string, HTMLElement[]>();

        tweets.forEach(tweet => {
          const authorElement = tweet.querySelector('[data-testid="User-Name"] a, [data-testid="User-Names"] a');
          if (authorElement) {
            const authorHandle = authorElement.getAttribute('href')?.split('/').pop();
            if (authorHandle) {
              authors.add(authorHandle);
              if (!tweetsByAuthor.has(authorHandle)) {
                tweetsByAuthor.set(authorHandle, []);
              }
              tweetsByAuthor.get(authorHandle)!.push(tweet as HTMLElement);
            }
          }
        });

        // Find the author with multiple tweets (likely the thread author)
        tweetsByAuthor.forEach((authorTweets, author) => {
          if (authorTweets.length > 1) {
            // Add only the first tweet as the thread container to avoid duplicates
            const firstTweet = authorTweets[0];
            if (firstTweet && !containers.includes(firstTweet)) {
              containers.push(firstTweet);
            }
          }
        });
      }
    } else {
      // On timeline, only look for explicit "Show this thread" links
      const showThreadLinks = document.querySelectorAll('a[href*="/status/"][role="link"]');
      showThreadLinks.forEach(link => {
        const linkText = link.textContent?.toLowerCase() || '';
        if (linkText.includes('show this thread') || linkText.includes('thread')) {
          const tweetContainer = link.closest('article[data-testid="tweet"]') as HTMLElement;
          if (tweetContainer && !containers.includes(tweetContainer)) {
            containers.push(tweetContainer);
          }
        }
      });
    }

    this.log(`Found ${containers.length} potential thread containers`);
    return containers;
  }

  /**
   * Find tweets connected to the given tweet (forming a thread)
   */
  private findConnectedTweets(startTweet: HTMLElement): HTMLElement[] {
    const connectedTweets: HTMLElement[] = [];
    const allTweets = document.querySelectorAll('article[data-testid="tweet"]');

    // Get the author of the start tweet
    const startAuthor = this.getTweetAuthor(startTweet);
    if (!startAuthor) return connectedTweets;

    // Look for tweets from the same author that appear to be connected
    allTweets.forEach(tweet => {
      if (tweet === startTweet) return;

      const tweetAuthor = this.getTweetAuthor(tweet as HTMLElement);
      if (tweetAuthor === startAuthor) {
        // Check if this tweet appears to be part of the same conversation
        const isConnected = this.areTweetsConnected(startTweet, tweet as HTMLElement);
        if (isConnected) {
          connectedTweets.push(tweet as HTMLElement);
        }
      }
    });

    return connectedTweets;
  }

  /**
   * Get the author handle from a tweet element
   */
  private getTweetAuthor(tweet: HTMLElement): string | null {
    const authorElement = tweet.querySelector('[data-testid="User-Name"] a, [data-testid="User-Names"] a');
    return authorElement?.getAttribute('href')?.split('/').pop() || null;
  }

  /**
   * Check if two tweets are connected (part of the same thread)
   */
  private areTweetsConnected(tweet1: HTMLElement, tweet2: HTMLElement): boolean {
    // Simple heuristic: if tweets are from the same author and appear close to each other
    // in the DOM, they're likely part of the same thread
    const tweet1Rect = tweet1.getBoundingClientRect();
    const tweet2Rect = tweet2.getBoundingClientRect();

    // Check if tweets are vertically close to each other
    const verticalDistance = Math.abs(tweet1Rect.bottom - tweet2Rect.top);
    return verticalDistance < 200; // Adjust threshold as needed
  }

  /**
   * Add a "Save Thread" button to a thread container
   */
  private addThreadSaveButton(container: HTMLElement): void {
    try {
      // Create thread save button
      const threadButton = this.createThreadSaveButton();

      // Find insertion point (similar to regular save button but with different styling)
      const actionBar = container.querySelector('div[role="group"]') as HTMLElement;

      if (actionBar) {
        // Create a container for the thread button
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
          display: flex;
          align-items: center;
          margin-left: 12px;
        `;
        buttonContainer.appendChild(threadButton);

        // Insert after the last action button
        const lastAction = Array.from(actionBar.children).pop();
        if (lastAction) {
          actionBar.insertBefore(buttonContainer, lastAction.nextSibling);
        } else {
          actionBar.appendChild(buttonContainer);
        }
      } else {
        // Fallback to absolute positioning
        threadButton.style.cssText += `
          position: absolute;
          top: 10px;
          right: 50px;
          z-index: 9999;
        `;
        container.appendChild(threadButton);
      }

      this.log('Thread save button added');
    } catch (error) {
      console.error('[Notely] Error adding thread save button:', error);
    }
  }

  /**
   * Create a "Save Thread" button
   */
  private createThreadSaveButton(): HTMLButtonElement {
    const button = document.createElement('button');
    button.className = 'notely-thread-save-btn';
    button.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
        <path d="M12 7v6M9 10h6"/>
      </svg>
      <span style="margin-left: 4px; font-size: 13px;">Thread</span>
    `;

    button.style.cssText = `
      display: flex;
      align-items: center;
      background: linear-gradient(135deg, #1d9bf0, #1a8cd8);
      color: white;
      border: none;
      border-radius: 20px;
      padding: 6px 12px;
      font-size: 13px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(29, 155, 240, 0.3);
    `;

    // Add hover effects
    button.addEventListener('mouseenter', () => {
      button.style.transform = 'scale(1.05)';
      button.style.boxShadow = '0 4px 8px rgba(29, 155, 240, 0.4)';
    });

    button.addEventListener('mouseleave', () => {
      button.style.transform = 'scale(1)';
      button.style.boxShadow = '0 2px 4px rgba(29, 155, 240, 0.3)';
    });

    // Add click handler
    button.addEventListener('click', (e) => {
      e.stopPropagation();
      this.handleThreadSaveClick(button);
    });

    return button;
  }

  /**
   * Send post data to background script with proper error handling
   */
  private async sendToBackground(postData: any): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if Chrome runtime is available
      if (!chrome?.runtime?.id) {
        reject(new Error('Extension context invalidated. Please reload the page and try again.'));
        return;
      }

      try {
        chrome.runtime.sendMessage({
          action: 'SAVE_POST_REQUEST',
          data: postData
        }, (response: any) => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Chrome runtime error: ${chrome.runtime.lastError.message}`));
          } else if (response?.status === 'success') {
            resolve();
          } else {
            reject(new Error(response?.message || 'Failed to save post'));
          }
        });
      } catch (error) {
        reject(new Error(`Failed to send message to background: ${error}`));
      }
    });
  }

  /**
   * Handle thread save button click
   */
  private async handleThreadSaveClick(button: HTMLButtonElement): Promise<void> {
    try {
      // Check if Chrome runtime is available before starting
      if (!chrome?.runtime?.id) {
        throw new Error('Extension context invalidated. Please reload the page and try again.');
      }

      // Update button state
      button.disabled = true;
      button.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <path d="M12 6v6l4 2"/>
        </svg>
        <span style="margin-left: 4px;">Saving...</span>
      `;

      // Extract all tweets in the thread
      const threadTweets = this.extractThreadTweets();

      if (threadTweets.length === 0) {
        throw new Error('No tweets found in thread');
      }

      this.log(`Saving thread with ${threadTweets.length} tweets`);

      // Generate a single thread ID for all tweets in the thread
      const threadId = this.generateThreadId();

      // Import the controller once outside the loop
      const { extractPostData } = await import('../controller');

      // Save each tweet in the thread
      const savePromises = threadTweets.map(async (tweet, index) => {
        try {
          const postData = await extractPostData(tweet, 'X/Twitter', {
            includeImages: true,
            useCloudService: true
          });

          if (postData) {
            // Add thread metadata
            postData.isThread = true;
            postData.threadId = threadId;
            postData.threadPosition = index + 1;
            postData.threadLength = threadTweets.length;

            // Send to background for processing with better error handling
            return this.sendToBackground(postData);
          }
        } catch (error) {
          console.error(`[Notely] Error saving tweet ${index + 1} in thread:`, error);
          throw error; // Re-throw to be caught by Promise.all
        }
      });

      await Promise.all(savePromises);

      // Update button to success state
      button.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M20 6L9 17l-5-5"/>
        </svg>
        <span style="margin-left: 4px;">Saved!</span>
      `;
      button.style.background = 'linear-gradient(135deg, #10b981, #059669)';

      // Reset button after delay
      setTimeout(() => {
        button.disabled = false;
        button.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
            <path d="M12 7v6M9 10h6"/>
          </svg>
          <span style="margin-left: 4px;">Thread</span>
        `;
        button.style.background = 'linear-gradient(135deg, #1d9bf0, #1a8cd8)';
      }, 3000);

    } catch (error) {
      console.error('[Notely] Error saving thread:', error);

      // Determine error message based on error type
      let errorMessage = 'Error';
      if (error instanceof Error) {
        if (error.message.includes('Extension context invalidated')) {
          errorMessage = 'Reload Page';
        } else if (error.message.includes('Chrome runtime')) {
          errorMessage = 'Reload Ext';
        }
      }

      // Update button to error state
      button.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <line x1="15" y1="9" x2="9" y2="15"/>
          <line x1="9" y1="9" x2="15" y2="15"/>
        </svg>
        <span style="margin-left: 4px;">${errorMessage}</span>
      `;
      button.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';

      setTimeout(() => {
        button.disabled = false;
        button.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
            <path d="M12 7v6M9 10h6"/>
          </svg>
          <span style="margin-left: 4px;">Thread</span>
        `;
        button.style.background = 'linear-gradient(135deg, #1d9bf0, #1a8cd8)';
      }, 3000);
    }
  }

  /**
   * Extract all tweets that are part of the current thread
   */
  private extractThreadTweets(): HTMLElement[] {
    const tweets: HTMLElement[] = [];
    const allTweetElements = document.querySelectorAll('article[data-testid="tweet"]');

    // If we're on a tweet detail page, collect all tweets from the same author
    const currentUrl = window.location.href;
    if (currentUrl.includes('/status/')) {
      const authors = new Set<string>();
      const tweetsByAuthor = new Map<string, HTMLElement[]>();

      allTweetElements.forEach(tweet => {
        const author = this.getTweetAuthor(tweet as HTMLElement);
        if (author) {
          authors.add(author);
          if (!tweetsByAuthor.has(author)) {
            tweetsByAuthor.set(author, []);
          }
          tweetsByAuthor.get(author)!.push(tweet as HTMLElement);
        }
      });

      // Find the author with the most tweets (likely the thread author)
      let maxTweets = 0;
      let threadAuthor = '';

      tweetsByAuthor.forEach((authorTweets, author) => {
        if (authorTweets.length > maxTweets) {
          maxTweets = authorTweets.length;
          threadAuthor = author;
        }
      });

      if (threadAuthor && maxTweets > 1) {
        tweets.push(...(tweetsByAuthor.get(threadAuthor) || []));
      }
    }

    // Fallback: if no thread detected, just get all visible tweets
    if (tweets.length === 0) {
      allTweetElements.forEach(tweet => {
        tweets.push(tweet as HTMLElement);
      });
    }

    return tweets;
  }

  /**
   * Generate a unique thread ID
   */
  private generateThreadId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `thread_${timestamp}_${random}`;
  }
}

// Initialize the Twitter content script
export const twitterScript = BaseContentScript.initializeWhenReady(TwitterContentScript);
