/**
 * Pinterest Content Script
 *
 * This script handles the extraction and saving of Pinterest pins.
 * It uses the modular platform architecture to handle Pinterest-specific logic.
 */

import { extractPostData } from './controller';

// Constants
const BUTTON_CLASS = 'notely-save-button';
const PROCESSED_ELEMENT_CLASS = 'notely-processed';
const POLLING_INTERVAL = 1500; // 1.5 seconds
const PORT_NAME = 'pinterest-save';

// Set of processed elements to avoid duplicate processing
const processedElements = new Set<Element>();

/**
 * Initialize the content script
 */
function initialize() {
  console.log('####### SSP (Pinterest): SCRIPT LOADED #######');
  console.log('SSP (Pinterest): Initializing polling...');

  // Start polling for pins
  startPolling();

  console.log(`Notely Social: Pinterest content script loaded and polling started (interval: ${POLLING_INTERVAL}ms).`);
}

/**
 * Start polling for pins
 */
function startPolling() {
  // Initial scan
  scanForPins();

  // Set up polling
  setInterval(scanForPins, POLLING_INTERVAL);
}

/**
 * Scan the page for pins
 */
function scanForPins() {
  // Find all pins on the page
  const pins = document.querySelectorAll('div[data-test-id="pin"]');

  // Process each pin
  pins.forEach(pin => {
    if (!pin.hasAttribute('data-ssp-processed')) {
      // Mark as processed
      pin.setAttribute('data-ssp-processed', 'true');

      // Set up mouse enter/leave handlers
      pin.addEventListener('mouseenter', () => handleMouseEnter(pin));
      pin.addEventListener('mouseleave', () => handleMouseLeave(pin));
    }
  });
}

/**
 * Handle mouse enter event
 */
function handleMouseEnter(pin: Element) {
  console.log(`SSP (Pinterest): ---> handleMouseEnter triggered. Attempting injection into: ${pin.outerHTML.substring(0, 100)}...`);

  // Check if the pin already has a save button
  if (pin.querySelector(`.${BUTTON_CLASS}`)) {
    return;
  }

  // Create the save button
  const saveButton = createSaveButton();

  // Add click event listener
  saveButton.addEventListener('click', (event) => {
    event.preventDefault();
    event.stopPropagation();
    handleSaveButtonClick(pin as HTMLElement, saveButton);
  });

  console.log(`SSP (Pinterest): Injecting save button into: ${pin.outerHTML.substring(0, 100)}...`);

  // Append the button to the pin
  pin.appendChild(saveButton);

  console.log(`SSP (Pinterest): >>> Button successfully appended to: ${pin.outerHTML.substring(0, 100)}...`);
}

/**
 * Handle mouse leave event
 */
function handleMouseLeave(pin: Element) {
  const saveButton = pin.querySelector(`.${BUTTON_CLASS}`) as HTMLButtonElement;
  console.log(`SSP (Pinterest): <--- handleMouseLeave triggered for: ${pin.outerHTML.substring(0, 100)}... Button found: ${!!saveButton}, Button disabled: ${saveButton?.disabled}`);

  // Remove the save button if it exists and is not disabled (in the middle of saving)
  if (saveButton && !saveButton.disabled) {
    console.log(`SSP (Pinterest): Mouse left, removing button from: ${pin.outerHTML.substring(0, 100)}...`);
    saveButton.remove();
  }
}

/**
 * Create a save button
 */
function createSaveButton(): HTMLButtonElement {
  const button = document.createElement('button');
  button.className = BUTTON_CLASS;
  button.textContent = 'Save to Notely';
  button.style.cssText = `
    position: absolute;
    bottom: 10px;
    left: 10px;
    z-index: 9999;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  `;

  return button;
}

/**
 * Handle save button click
 */
async function handleSaveButtonClick(pin: HTMLElement, button: HTMLButtonElement) {
  console.log('SSP (Pinterest): Save button CLICKED!');

  // Update button state
  button.textContent = 'Saving...';
  button.disabled = true;
  button.style.backgroundColor = '#999999';

  try {
    // Extract pin data directly using our platform service
    console.log(`SSP (Pinterest): Extracting basic data from feed element: ${pin.outerHTML.substring(0, 100)}...`);

    // Extract and save the pin using the new queue-based approach
    const postData = await extractPostData(pin, 'pinterest', { includeImages: true });

    if (!postData) {
      button.textContent = 'Error';
      button.style.backgroundColor = '#e0245e';
      console.error('SSP (Pinterest): Failed to extract Pinterest pin data');
      return;
    }

    // Send to background for AI processing, local save, and cloud sync
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'SAVE_POST_REQUEST',
        data: postData
      });

      if (response?.status === 'success') {
        button.textContent = 'Saved!';
        button.style.backgroundColor = '#17bf63';
        console.log(`SSP (Pinterest): Pinterest pin ${postData.id} sent for processing and save`);
      } else {
        button.textContent = 'Error';
        button.style.backgroundColor = '#e0245e';
        console.error(`SSP (Pinterest): Background processing failed for Pinterest pin: ${response?.message}`);
      }
    } catch (backgroundError) {
      button.textContent = 'Error';
      button.style.backgroundColor = '#e0245e';
      console.error(`SSP (Pinterest): Error sending Pinterest pin to background:`, backgroundError);
    }

    // Keep the success/error message visible for a moment
    setTimeout(() => {
      if (button.parentNode) {
        button.textContent = 'Save to Notely';
        button.disabled = false;
        button.style.backgroundColor = '#4CAF50';
      }
    }, 3000);
  } catch (error) {
    button.textContent = 'Error';
    button.style.backgroundColor = '#e0245e';
    console.error('SSP (Pinterest): Error in handleSaveButtonClick:', error);

    // Re-enable button after error
    setTimeout(() => {
      if (button.parentNode) {
        button.textContent = 'Save to Notely';
        button.disabled = false;
        button.style.backgroundColor = '#4CAF50';
      }
    }, 3000);
  }
}

// Initialize the content script when the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}
