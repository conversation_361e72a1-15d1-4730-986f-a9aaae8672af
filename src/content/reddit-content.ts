/**
 * Reddit Content Script
 *
 * This script handles the extraction and saving of Reddit posts.
 * It uses the modular platform architecture to handle Reddit-specific logic.
 */

import { extractPostData } from './controller';
import { getStandardButtonStyle, getSavingButtonStyle, getSavedButtonStyle, getErrorButtonStyle, getAbsoluteButtonStyle } from '../styles/button-styles';

// Constants
const SAVE_BUTTON_CLASS = 'notely-save-button';
const PROCESSED_ELEMENT_CLASS = 'notely-processed';
// Updated selectors for modern Reddit (2024)
const POST_SELECTOR = [
  // New Reddit selectors
  'shreddit-post',
  'article[data-testid="post-container"]',
  'div[data-testid="post-container"]',
  'div[slot="full-post-link"]',
  'faceplate-tracker[data-faceplate-tracking-context*="post"]',
  // Old Reddit selectors (fallback)
  'div.Post',
  'div.scrollerItem',
  'div._1oQyIsiPHYt6nx7VOmd1sz',
  'article.Post',
  // Additional modern selectors
  'div[data-click-id="body"]',
  'div[data-adclicklocation="body"]'
].join(', ');

// Set of processed elements to avoid duplicate processing
const processedElements = new Set<Element>();

/**
 * Initialize the content script
 */
function initialize() {
  console.log('[Notely] Reddit content script initialized');

  // Start scanning for posts
  scanForPosts();

  // Set up mutation observer to detect new posts
  setupMutationObserver();
}

/**
 * Scan the page for Reddit posts
 */
function scanForPosts() {
  // Debug: Log what we're looking for
  console.log(`[Notely] Scanning for Reddit posts with selector: ${POST_SELECTOR}`);

  // Find all posts on the page
  const posts = document.querySelectorAll(POST_SELECTOR);
  console.log(`[Notely] Found ${posts.length} Reddit posts`);

  // Debug: If no posts found, try to identify what elements are available
  if (posts.length === 0) {
    console.log('[Notely] No posts found. Debugging available elements...');

    // Check for common Reddit elements
    const debugSelectors = [
      'shreddit-post',
      'article',
      'div[data-testid]',
      'faceplate-tracker',
      'div[slot]'
    ];

    debugSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        console.log(`[Notely] Found ${elements.length} elements with selector: ${selector}`);
        // Log first element's attributes for debugging
        if (elements[0]) {
          const attrs = Array.from(elements[0].attributes).map(attr => `${attr.name}="${attr.value}"`);
          console.log(`[Notely] First ${selector} element attributes:`, attrs.join(', '));
        }
      }
    });
  }

  // Process each post
  posts.forEach(post => {
    if (!processedElements.has(post) && !post.classList.contains(PROCESSED_ELEMENT_CLASS)) {
      processedElements.add(post);
      post.classList.add(PROCESSED_ELEMENT_CLASS);
      addSaveButton(post as HTMLElement);
    }
  });
}

/**
 * Add a save button to a Reddit post
 */
function addSaveButton(post: HTMLElement) {
  // Create the save button with standardized styling
  const saveButton = document.createElement('button');
  saveButton.className = SAVE_BUTTON_CLASS;
  saveButton.textContent = 'Save to Notely';
  saveButton.style.cssText = getStandardButtonStyle('Reddit');

  // Add click event listener
  saveButton.addEventListener('click', (event) => {
    event.preventDefault();
    event.stopPropagation();
    handleSaveButtonClick(post, saveButton);
  });

  // Find the appropriate location to insert the button (updated for modern Reddit)
  const actionBar = post.querySelector([
    // Modern Reddit selectors
    'div[slot="actionRow"]',
    'div[data-testid="post-engagement-bar"]',
    'div[data-testid="post-social-buttons"]',
    'faceplate-partial[src*="engagement"]',
    'div.flex.items-center.gap-xs',
    // Legacy selectors
    '.reddit-infobar',
    '.bottom-row',
    '[data-testid="post-comment-header"]',
    '.post-buttons',
    '.action-buttons',
    '.ButtonGroup',
    '._3-ITD33yeeJXxZ8g6Z_IRd'
  ].join(', '));

  if (actionBar) {
    actionBar.appendChild(saveButton);
  } else {
    // Position the button in a visible location with absolute positioning
    saveButton.style.cssText += getAbsoluteButtonStyle();

    // Find a container element to append to (updated for modern Reddit)
    const container = post.querySelector([
      // Modern Reddit selectors
      'div[slot="title"]',
      'div[slot="text-body"]',
      'faceplate-partial[src*="post-title"]',
      'div[data-testid="post-content"]',
      // Legacy selectors
      '.top-matter',
      '.post-header',
      '[data-testid="post-container"]',
      '.Post-header',
      '.post-content'
    ].join(', ')) || post;

    // Ensure container is treated as HTMLElement for TypeScript
    if (container instanceof HTMLElement) {
      container.style.position = 'relative';
      container.appendChild(saveButton);
    } else {
      // Fallback if container is not an HTMLElement
      post.appendChild(saveButton);
    }
  }
}

/**
 * Handle save button click
 */
async function handleSaveButtonClick(post: HTMLElement, button: HTMLButtonElement) {
  // Update button state
  button.textContent = 'Saving...';
  button.disabled = true;
  button.style.cssText += getSavingButtonStyle();

  try {
    // First extract the post data with images for queue processing
    const postData = await extractPostData(post, 'Reddit', { includeImages: true });

    if (!postData) {
      button.textContent = 'Error';
      button.style.cssText += getErrorButtonStyle();
      console.error('[Notely] Failed to extract Reddit post data');

      setTimeout(() => {
        button.textContent = 'Save to Notely';
        button.disabled = false;
        button.style.cssText = getStandardButtonStyle('Reddit');
      }, 3000);
      return;
    }

    // Send to background for AI processing, local save, and cloud sync
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'SAVE_POST_REQUEST',
        data: postData
      });

      if (response?.status === 'success') {
        button.textContent = 'Saved!';
        button.style.cssText += getSavedButtonStyle();
        console.log(`[Notely] Reddit post ${postData.id} sent for processing and save`);
      } else {
        button.textContent = 'Error';
        button.style.cssText += getErrorButtonStyle();
        console.error(`[Notely] Background processing failed for Reddit post: ${response?.message}`);

        // Re-enable button after error
        setTimeout(() => {
          button.textContent = 'Save to Notely';
          button.disabled = false;
          button.style.cssText = getStandardButtonStyle('Reddit');
        }, 3000);
      }
    } catch (backgroundError) {
      button.textContent = 'Error';
      button.style.cssText += getErrorButtonStyle();
      console.error(`[Notely] Error sending Reddit post to background:`, backgroundError);

      // Re-enable button after error
      setTimeout(() => {
        button.textContent = 'Save to Notely';
        button.disabled = false;
        button.style.cssText = getStandardButtonStyle('Reddit');
      }, 3000);
    }
  } catch (error) {
    button.textContent = 'Error';
    button.style.cssText += getErrorButtonStyle();
    console.error('[Notely] Error in handleSaveButtonClick:', error);

    // Re-enable button after error
    setTimeout(() => {
      button.textContent = 'Save to Notely';
      button.disabled = false;
      button.style.cssText = getStandardButtonStyle('Reddit');
    }, 3000);
  }
}

/**
 * Set up mutation observer to detect new posts
 */
function setupMutationObserver() {
  const observer = new MutationObserver((mutations) => {
    let shouldScan = false;

    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        shouldScan = true;
        break;
      }
    }

    if (shouldScan) {
      scanForPosts();
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  console.log('[Notely] Mutation observer set up for Reddit');
}

// Initialize the content script when the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}
