/**
 * Button Manager - Unified button creation and styling for all platforms
 * 
 * This module provides consistent button styling and behavior across all social media platforms.
 * It centralizes button creation, state management, and styling to avoid duplication.
 */

import { Platform } from '../../types';

export interface ButtonConfig {
  text?: string;
  className?: string;
  position?: 'inline' | 'absolute';
  platform?: Platform;
}

export interface ButtonState {
  idle: string;
  saving: string;
  saved: string;
  error: string;
}

export class ButtonManager {
  private static readonly SAVE_BUTTON_CLASS = 'notely-save-button';
  private static readonly PROCESSED_ELEMENT_CLASS = 'notely-processed';

  private static readonly DEFAULT_BUTTON_STATES: ButtonState = {
    idle: 'Save to Notely',
    saving: 'Saving...',
    saved: 'Saved!',
    error: 'Error'
  };

  /**
   * Create a standardized save button
   */
  static createSaveButton(config: ButtonConfig = {}): HTMLButtonElement {
    const button = document.createElement('button');
    button.className = config.className || this.SAVE_BUTTON_CLASS;
    button.textContent = config.text || this.DEFAULT_BUTTON_STATES.idle;
    
    // Apply base styles
    button.style.cssText = this.getButtonStyles(config.platform, config.position);
    
    return button;
  }

  /**
   * Get platform-specific button styles
   */
  private static getButtonStyles(platform?: Platform, position: 'inline' | 'absolute' = 'inline'): string {
    const baseStyles = `
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 6px 10px;
      margin: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: bold;
      transition: background-color 0.3s ease;
      z-index: 9999;
    `;

    const positionStyles = position === 'absolute' ? `
      position: absolute;
      top: 10px;
      right: 10px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    ` : '';

    // Platform-specific adjustments
    const platformStyles = this.getPlatformSpecificStyles(platform);

    return baseStyles + positionStyles + platformStyles;
  }

  /**
   * Get platform-specific style adjustments
   */
  private static getPlatformSpecificStyles(platform?: Platform): string {
    switch (platform) {
      case 'X/Twitter':
        return `
          border-radius: 20px;
          font-size: 11px;
          padding: 4px 8px;
        `;
      case 'Instagram':
        return `
          border-radius: 6px;
          font-size: 11px;
        `;
      case 'LinkedIn':
        return `
          border-radius: 2px;
          font-size: 12px;
          padding: 5px 12px;
        `;
      default:
        return '';
    }
  }

  /**
   * Update button state with appropriate styling
   */
  static updateButtonState(
    button: HTMLButtonElement, 
    state: keyof ButtonState, 
    customText?: string
  ): void {
    const text = customText || this.DEFAULT_BUTTON_STATES[state];
    button.textContent = text;

    // Update styling based on state
    switch (state) {
      case 'saving':
        button.disabled = true;
        button.style.backgroundColor = '#999999';
        button.style.cursor = 'not-allowed';
        break;
      case 'saved':
        button.disabled = false;
        button.style.backgroundColor = '#17bf63';
        button.style.cursor = 'pointer';
        break;
      case 'error':
        button.disabled = false;
        button.style.backgroundColor = '#e0245e';
        button.style.cursor = 'pointer';
        break;
      case 'idle':
      default:
        button.disabled = false;
        button.style.backgroundColor = '#4CAF50';
        button.style.cursor = 'pointer';
        break;
    }
  }

  /**
   * Reset button to idle state after a delay
   */
  static resetButtonAfterDelay(
    button: HTMLButtonElement, 
    delay: number = 3000,
    platform?: Platform
  ): void {
    setTimeout(() => {
      if (button.parentNode) {
        this.updateButtonState(button, 'idle');
        // Reapply platform-specific styles
        button.style.cssText = this.getButtonStyles(platform);
      }
    }, delay);
  }

  /**
   * Add click handler with standard behavior
   */
  static addClickHandler(
    button: HTMLButtonElement,
    handler: (event: MouseEvent) => void | Promise<void>
  ): void {
    button.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      handler(event);
    });
  }

  /**
   * Check if element has already been processed
   */
  static isProcessed(element: Element): boolean {
    return element.classList.contains(this.PROCESSED_ELEMENT_CLASS);
  }

  /**
   * Mark element as processed
   */
  static markAsProcessed(element: Element): void {
    element.classList.add(this.PROCESSED_ELEMENT_CLASS);
  }

  /**
   * Get the save button class name
   */
  static getSaveButtonClass(): string {
    return this.SAVE_BUTTON_CLASS;
  }

  /**
   * Get the processed element class name
   */
  static getProcessedElementClass(): string {
    return this.PROCESSED_ELEMENT_CLASS;
  }

  /**
   * Find the best insertion point for a button in a post element
   */
  static findButtonInsertionPoint(
    postElement: HTMLElement,
    platform: Platform,
    selectors: string[] = []
  ): { container: HTMLElement; method: 'append' | 'prepend' | 'absolute' } {
    // Platform-specific selectors for action bars/footers
    const platformSelectors = this.getPlatformSelectors(platform);
    const allSelectors = [...selectors, ...platformSelectors];

    // Try to find an appropriate container
    for (const selector of allSelectors) {
      const container = postElement.querySelector(selector) as HTMLElement;
      if (container) {
        return { container, method: 'append' };
      }
    }

    // Fallback to absolute positioning
    return { container: postElement, method: 'absolute' };
  }

  /**
   * Get platform-specific selectors for button placement
   */
  private static getPlatformSelectors(platform: Platform): string[] {
    switch (platform) {
      case 'X/Twitter':
        return [
          'div[role="group"]',
          '[data-testid="tweet"] footer',
          '[data-testid="tweetDetail"] footer'
        ];
      case 'Instagram':
        return [
          'section',
          '.post-actions',
          'article footer'
        ];
      case 'LinkedIn':
        return [
          '.update-components-footer',
          '.feed-shared-social-actions',
          '.social-details-social-actions',
          '.feed-shared-social-action-bar'
        ];
      default:
        return [];
    }
  }
}
