import translations from '../translations';

interface Translations {
  [key: string]: {
    en: string;
    tr: string;
    fr: string;
    de: string;
    es: string;
    [key: string]: string;
  };
}

// Supported locales
export const SUPPORTED_LOCALES = ['en', 'tr', 'fr', 'de', 'es'] as const;
export type SupportedLocale = typeof SUPPORTED_LOCALES[number];

// Cache for performance optimization
const translationCache = new Map<string, string>();

/**
 * Main translation function with caching and fallback support
 */
function t(key: string, locale: string): string {
  // Create cache key
  const cacheKey = `${key}:${locale}`;

  // Check cache first
  if (translationCache.has(cacheKey)) {
    return translationCache.get(cacheKey)!;
  }

  const translationData = translations as Translations;
  let result: string;

  // Try requested locale
  if (translationData[key] && translationData[key][locale]) {
    result = translationData[key][locale];
  }
  // Fallback to English
  else if (translationData[key] && translationData[key].en) {
    result = translationData[key].en;
  }
  // Return key if no translation found
  else {
    result = key;
    // Log missing translation in development
    if (process.env.NODE_ENV === 'development') {
      console.warn(`Missing translation for key: ${key} in locale: ${locale}`);
    }
  }

  // Cache the result
  translationCache.set(cacheKey, result);

  return result;
}

/**
 * Get browser locale with fallback
 */
export function getBrowserLocale(): SupportedLocale {
  if (typeof navigator !== 'undefined') {
    const browserLang = navigator.language.split('-')[0];
    return SUPPORTED_LOCALES.includes(browserLang as SupportedLocale)
      ? browserLang as SupportedLocale
      : 'en';
  }
  return 'en';
}

/**
 * Validate if a locale is supported
 */
export function isSupportedLocale(locale: string): locale is SupportedLocale {
  return SUPPORTED_LOCALES.includes(locale as SupportedLocale);
}

/**
 * Get current locale from Chrome storage with fallback
 */
export async function getCurrentLocaleAsync(): Promise<SupportedLocale> {
  return new Promise((resolve) => {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.sync.get({ locale: 'en' }, (result: { locale: string }) => {
        const locale = result.locale;
        resolve(isSupportedLocale(locale) ? locale : 'en');
      });
    } else {
      resolve(getBrowserLocale());
    }
  });
}

/**
 * Set locale in Chrome storage
 */
export async function setLocale(locale: SupportedLocale): Promise<void> {
  return new Promise((resolve) => {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.sync.set({ locale }, () => {
        // Clear cache when locale changes
        translationCache.clear();
        resolve();
      });
    } else {
      resolve();
    }
  });
}

/**
 * Clear translation cache (useful for testing or locale changes)
 */
export function clearTranslationCache(): void {
  translationCache.clear();
}

export default t;
