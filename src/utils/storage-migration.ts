import { Post } from '../types';
import { StorageService } from '../services/storage';

export async function migrateToIndexedDB(): Promise<{ success: boolean; migrated: number }> {
  const storageService = new StorageService();
  let migratedCount = 0;

  try {
    // Get all posts from Chrome sync storage
    const syncResult = await new Promise<{ savedPosts?: Post[] }>((resolve) => {
      chrome.storage.sync.get('savedPosts', resolve);
    });

    // Get all posts from Chrome local storage
    const localResult = await new Promise<{ localSavedPosts?: Post[] }>((resolve) => {
      chrome.storage.local.get('localSavedPosts', resolve);
    });

    // Get legacy localStorage posts if they exist
    let legacyPosts: Post[] = [];
    try {
      const localData = localStorage.getItem('ssp_savedPosts');
      if (localData) {
        legacyPosts = JSON.parse(localData);
      }
    } catch (e) {
      console.warn('Failed to parse legacy localStorage posts:', e);
    }

    // Combine all posts, removing duplicates by permalink
    const allPosts = new Map<string, Post>();
    
    const addToMap = (posts: Post[] | undefined, source: string) => {
      if (posts) {
        posts.forEach(post => {
          if (!allPosts.has(post.permalink)) {
            allPosts.set(post.permalink, { ...post, source });
          }
        });
      }
    };

    addToMap(syncResult.savedPosts, 'sync');
    addToMap(localResult.localSavedPosts, 'local');
    addToMap(legacyPosts, 'legacy');

    // Migrate posts to IndexedDB
    console.log(`Starting migration of ${allPosts.size} posts to IndexedDB...`);
    
    for (const post of allPosts.values()) {
      const result = await storageService.savePost(post);
      if (result.success) {
        migratedCount++;
      }
    }

    // Clear old storage after successful migration
    if (migratedCount > 0) {
      try {
        await chrome.storage.sync.remove('savedPosts');
        await chrome.storage.local.remove('localSavedPosts');
        localStorage.removeItem('ssp_savedPosts');
        console.log('Old storage cleared successfully');
      } catch (e) {
        console.warn('Failed to clear some old storage:', e);
      }
    }

    return { success: true, migrated: migratedCount };
  } catch (error) {
    console.error('Migration failed:', error);
    return { success: false, migrated: migratedCount };
  }
} 