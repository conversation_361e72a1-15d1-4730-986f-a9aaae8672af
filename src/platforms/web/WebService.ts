/**
 * Web Platform Service
 *
 * This service handles the extraction and saving of web content from any website.
 * It supports text selection, image capture, and page metadata extraction.
 */

import { Post, Platform } from '../../types';
import { BasePlatformService } from '../base/BasePlatformService';
import { ExtractionOptions, SaveOptions, ExtractionResult, SaveResult, ImageExtractionResult } from '../base/types';
import { savePost } from '../../storage';

export class WebService extends BasePlatformService {
  protected platform: Platform = 'Web';

  /**
   * Extract post data from web content
   * This can be called from context menu actions or content script
   */
  async extractPostData(
    element: HTMLElement,
    options: ExtractionOptions = {}
  ): Promise<ExtractionResult> {
    try {
      console.log('[WebService] Extracting web content');

      // Get page metadata
      const url = window.location.href;
      const title = document.title;
      const domain = new URL(url).hostname;

      // Get favicon
      const favicon = this.getFavicon();

      // Extract selected text or provided content
      let content = '';
      let selectedText = '';

      // Check if there's selected text
      const selection = window.getSelection();
      if (selection && selection.toString().trim()) {
        selectedText = selection.toString().trim();
        content = selectedText;
      }

      // If no selection, try to get content from element or use page title
      if (!content && element) {
        content = element.textContent?.trim() || title;
      }

      if (!content) {
        content = title;
      }

      // Extract images if any
      let imageUrl = '';
      let imageAlt = '';

      // Check if element is an image or contains images
      if (element) {
        if (element.tagName === 'IMG') {
          const img = element as HTMLImageElement;
          imageUrl = img.src;
          imageAlt = img.alt || '';
        } else {
          // Look for images within the element
          const img = element.querySelector('img');
          if (img) {
            imageUrl = img.src;
            imageAlt = img.alt || '';
          }
        }
      }

      // Create post object
      const post: Post = {
        id: `web-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        platform: this.platform,
        author: domain,
        authorName: domain,
        authorHandle: domain,
        authorUrl: url,
        authorAvatar: favicon,
        content: content,
        textContent: content,
        title: title,
        url: url,
        permalink: url,
        timestamp: new Date().toISOString(),
        savedAt: new Date().toISOString(),
        media: imageUrl ? [{
          type: 'image' as const,
          url: imageUrl,
          alt: imageAlt
        }] : undefined,
        source: 'local'
      };

      console.log('[WebService] Extracted web content:', post);

      return {
        success: true,
        post: post
      };

    } catch (error) {
      console.error('[WebService] Error extracting web content:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Process and save web content
   */
  async processAndSavePost(
    post: Post,
    options: SaveOptions = {}
  ): Promise<SaveResult> {
    try {
      console.log('[WebService] Processing and saving web content');

      // Add any web-specific processing here
      const processedPost = {
        ...post,
        platform: this.platform,
        isProcessed: true
      };

      // Save to local storage
      const savedLocally = await this.saveToLocal(processedPost);

      if (!savedLocally) {
        return {
          success: false,
          error: 'Failed to save post to local storage'
        };
      }

      // If user is logged in and cloud save is enabled, save to cloud
      if (options.saveToCloud) {
        try {
          await this.saveToCloud(processedPost);
        } catch (error) {
          console.warn('[WebService] Failed to save to cloud, but local save succeeded:', error);
          // Don't fail the entire operation if cloud save fails
        }
      }

      return {
        success: true,
        postId: processedPost.id
      };

    } catch (error) {
      console.error('[WebService] Error processing and saving web content:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get the favicon for the current page
   */
  private getFavicon(): string {
    // Try to find favicon link elements
    const faviconLink = document.querySelector('link[rel="icon"]') as HTMLLinkElement ||
                       document.querySelector('link[rel="shortcut icon"]') as HTMLLinkElement ||
                       document.querySelector('link[rel="apple-touch-icon"]') as HTMLLinkElement;

    if (faviconLink && faviconLink.href) {
      return faviconLink.href;
    }

    // Fallback to default favicon path
    const domain = window.location.hostname;
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
  }

  /**
   * Extract content from selected text and context
   */
  extractFromSelection(): { content: string; context: string } | null {
    const selection = window.getSelection();
    if (!selection || selection.toString().trim() === '') {
      return null;
    }

    const selectedText = selection.toString().trim();

    // Get some context around the selection
    let context = '';
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const container = range.commonAncestorContainer;

      // Try to get the parent element's text for context
      if (container.nodeType === Node.TEXT_NODE && container.parentElement) {
        context = container.parentElement.textContent?.trim() || '';
      } else if (container.nodeType === Node.ELEMENT_NODE) {
        context = (container as Element).textContent?.trim() || '';
      }
    }

    return {
      content: selectedText,
      context: context
    };
  }

  /**
   * Extract image information from an element
   */
  extractImageInfo(element: HTMLElement): { url: string; alt: string; title?: string } | null {
    if (element.tagName === 'IMG') {
      const img = element as HTMLImageElement;
      return {
        url: img.src,
        alt: img.alt || '',
        title: img.title || ''
      };
    }

    // Check if element has a background image
    const computedStyle = window.getComputedStyle(element);
    const backgroundImage = computedStyle.backgroundImage;

    if (backgroundImage && backgroundImage !== 'none') {
      // Extract URL from background-image CSS property
      const urlMatch = backgroundImage.match(/url\(['"]?([^'"]+)['"]?\)/);
      if (urlMatch && urlMatch[1]) {
        return {
          url: urlMatch[1],
          alt: element.getAttribute('aria-label') || element.getAttribute('title') || '',
          title: element.getAttribute('title') || ''
        };
      }
    }

    return null;
  }

  /**
   * Extract image URLs from web content
   * @param source The post data or DOM element to extract images from
   * @param options Options for extraction
   * @returns ImageExtractionResult containing the extracted images or an error
   */
  async extractImageUrls(source: Post | HTMLElement, options?: ExtractionOptions): Promise<ImageExtractionResult> {
    try {
      const images: any[] = [];

      if (source instanceof HTMLElement) {
        // Extract from DOM element
        const imageInfo = this.extractImageInfo(source);
        if (imageInfo) {
          images.push({
            type: 'image' as const,
            url: imageInfo.url,
            alt: imageInfo.alt,
            title: imageInfo.title
          });
        }

        // Also look for images within the element
        const imgElements = source.querySelectorAll('img');
        imgElements.forEach(img => {
          if (img.src) {
            images.push({
              type: 'image' as const,
              url: img.src,
              alt: img.alt || '',
              title: img.title || ''
            });
          }
        });
      } else {
        // Extract from post data
        if (source.media) {
          images.push(...source.media);
        }
      }

      return {
        success: true,
        images
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Save post data to local storage
   * @param post The post data to save
   * @returns Promise<boolean> indicating success or failure
   */
  async saveToLocal(post: Post): Promise<boolean> {
    try {
      const result = await savePost(post);
      return result.status === 'success';
    } catch (error) {
      console.error('Error saving web content to local storage:', error);
      return false;
    }
  }

  /**
   * Upload post data to cloud storage
   * @param post The post data to upload
   * @returns Promise<boolean> indicating success or failure
   */
  async uploadToCloud(post: Post): Promise<boolean> {
    try {
      // For web content, we don't have a specific cloud upload implementation yet
      // This could be implemented to send to the backend API
      console.log('[WebService] Cloud upload not implemented for web content yet');
      return true; // Return true to not fail the save operation
    } catch (error) {
      console.error('Error uploading web content to cloud:', error);
      return false;
    }
  }
}
