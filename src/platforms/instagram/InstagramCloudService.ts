/**
 * Instagram Cloud Service
 *
 * This version sends data to the backend for server-side processing.
 * The backend handles image fetching, S3 uploads, and database storage.
 */

import { Post, Platform, MediaItem } from '../../types';
import { BasePlatformService } from '../base/BasePlatformService';
import { ExtractionOptions, SaveOptions, ExtractionResult, SaveResult } from '../base/types';
import * as authService from '../../services/authService';
import { savePost } from '../../storage';

// Instagram-specific selectors
const SELECTORS = {
  // Author selectors
  AUTHOR: 'header a, a[href^="/"][role="link"], a[href^="/"] span, div[role="button"] span',
  AUTHOR_HANDLE_SPAN: 'header span, a[href^="/"] span',
  AUTHOR_AVATAR: 'img[alt$="\'s profile picture"][draggable="false"]',

  // Content selectors
  CAPTION: 'div[role="button"] + div, div[role="button"] + span, div[dir="auto"], span[dir="auto"], div._a9zs',

  // Image selectors
  IMAGE: 'article img[src]:not([src^="data:"]):not([alt*="profile picture"])',
  CAROUSEL_ITEM: 'div[role="button"] img[src]:not([src^="data:"])',

  // Interaction selectors
  LIKES_LINK_SPAN: 'a[href*="liked_by"] span, section span:first-of-type',
  COMMENTS_LINK_SPAN: 'a[href*="comments"] span, span + span'
};

export class InstagramCloudService extends BasePlatformService {
  constructor() {
    super('Instagram');
  }

  /**
   * Extract post data from an Instagram post element
   * @param element The post element
   * @param options Options for extraction
   * @returns ExtractionResult containing the post data or an error
   */
  async extractPostData(element: HTMLElement, options?: ExtractionOptions): Promise<ExtractionResult> {
    try {
      console.log('Instagram Cloud: Extracting post data');

      // --- Post ID ---
      // Extract the post ID (shortcode) from the URL
      let shortcode = '';
      console.log('Instagram Cloud: Attempting to extract shortcode...');

      const linkElement = element.querySelector('a[href*="/p/"]');
      console.log('Instagram Cloud: Post link element found:', !!linkElement);

      if (linkElement instanceof HTMLAnchorElement) {
        console.log('Instagram Cloud: Post link href:', linkElement.href);
        const match = linkElement.href.match(/\/p\/([^/?]+)/);
        if (match && match[1]) {
          shortcode = match[1];
          console.log('Instagram Cloud: Shortcode from post link:', shortcode);
        }
      }

      // If no shortcode found, try to find it in a time element
      if (!shortcode) {
        console.log('Instagram Cloud: Trying time element...');
        const timeElement = element.querySelector('time');
        console.log('Instagram Cloud: Time element found:', !!timeElement);

        if (timeElement && timeElement.parentElement instanceof HTMLAnchorElement) {
          console.log('Instagram Cloud: Time parent link href:', timeElement.parentElement.href);
          const match = timeElement.parentElement.href.match(/\/p\/([^/?]+)/);
          if (match && match[1]) {
            shortcode = match[1];
            console.log('Instagram Cloud: Shortcode from time element:', shortcode);
          }
        }
      }

      // Try alternative strategies
      if (!shortcode) {
        console.log('Instagram Cloud: Trying alternative strategies...');

        // Try current URL
        const urlMatch = window.location.href.match(/\/p\/([^/?]+)/);
        if (urlMatch && urlMatch[1]) {
          shortcode = urlMatch[1];
          console.log('Instagram Cloud: Shortcode from current URL:', shortcode);
        }

        // Try reel URLs
        if (!shortcode) {
          const reelElement = element.querySelector('a[href*="/reel/"]');
          if (reelElement instanceof HTMLAnchorElement) {
            const match = reelElement.href.match(/\/reel\/([^/?]+)/);
            if (match && match[1]) {
              shortcode = match[1];
              console.log('Instagram Cloud: Shortcode from reel link:', shortcode);
            }
          }
        }
      }

      if (!shortcode) {
        console.error('Instagram Cloud: Could not extract post ID (shortcode)');
        console.error('Instagram Cloud: Element HTML snippet:', element.outerHTML.substring(0, 500));
        return {
          success: false,
          error: 'Could not extract post ID (shortcode)'
        };
      }

      // Construct the permalink
      const postUrl = `https://www.instagram.com/p/${shortcode}/`;
      console.log(`Instagram Cloud: Found shortcode: ${shortcode}`);

      // --- Author Info ---
      // Try different selectors for author information
      let author = '';
      let authorUrl = '';

      // First try the header link
      const authorElement = element.querySelector(SELECTORS.AUTHOR);
      if (authorElement) {
        author = authorElement.textContent?.trim() || '';
        if (authorElement instanceof HTMLAnchorElement) {
          authorUrl = authorElement.href;
        }
      }

      // If no author found, try the handle span
      if (!author) {
        const authorHandleSpan = element.querySelector(SELECTORS.AUTHOR_HANDLE_SPAN);
        if (authorHandleSpan) {
          author = authorHandleSpan.textContent?.trim() || '';
          // Try to find the parent link
          const parentLink = authorHandleSpan.closest('a');
          if (parentLink instanceof HTMLAnchorElement) {
            authorUrl = parentLink.href;
          }
        }
      }

      // Extract author avatar and convert to base64
      let authorAvatar = '';
      const authorAvatarElement = element.querySelector(SELECTORS.AUTHOR_AVATAR);
      if (authorAvatarElement instanceof HTMLImageElement) {
        try {
          // Convert author avatar to base64 to avoid CORS issues
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (ctx) {
            canvas.width = authorAvatarElement.naturalWidth || authorAvatarElement.width || 150;
            canvas.height = authorAvatarElement.naturalHeight || authorAvatarElement.height || 150;

            // Draw the avatar image onto the canvas
            ctx.drawImage(authorAvatarElement, 0, 0, canvas.width, canvas.height);

            // Convert canvas to data URL
            authorAvatar = canvas.toDataURL('image/jpeg', 0.9);
            console.log(`Instagram Cloud: Successfully converted author avatar to base64 (${authorAvatar.length} chars)`);
          } else {
            // Fallback to original URL if canvas fails
            authorAvatar = authorAvatarElement.src;
            console.log(`Instagram Cloud: Canvas context failed, using original avatar URL: ${authorAvatar.substring(0, 50)}...`);
          }
        } catch (error) {
          console.error('Instagram Cloud: Error converting author avatar to base64:', error);
          // Fallback to original URL if conversion fails
          authorAvatar = authorAvatarElement.src;
        }
      }

      console.log(`Instagram Cloud: Author: ${author}, Avatar URL: ${authorAvatar ? 'Found' : 'Not found'}`);

      // --- Post Content ---
      // Extract post caption with improved logic
      let caption = '';

      // Try to find the actual post caption by looking for specific patterns
      // Look for spans that contain the actual post text (usually longer content)
      const captionCandidates = element.querySelectorAll('span, div[dir="auto"]');

      for (const candidate of captionCandidates) {
        const text = candidate.textContent?.trim() || '';

        // Skip if it's likely not a caption
        if (text.length < 10) continue; // Too short
        if (text === author) continue; // Just the username
        if (text.includes('Suggested for you')) continue; // Instagram UI text
        if (text.includes('Follow')) continue; // Follow button
        if (text.includes('ago')) continue; // Timestamp
        if (text.includes('likes')) continue; // Like count
        if (text.includes('comments')) continue; // Comment count
        if (text.includes('Share')) continue; // Share button
        if (text.includes('Save')) continue; // Save button
        if (text.includes('More options')) continue; // Menu
        if (text.match(/^\d+$/)) continue; // Just numbers
        if (text.match(/^@\w+$/)) continue; // Just mentions

        // If we find a longer text that seems like actual content, use it
        if (text.length > caption.length && text.length > 15) {
          caption = text;
        }
      }

      // If still no caption, try a more specific approach
      if (!caption) {
        // Look for the post content in article descendants
        const articleSpans = element.querySelectorAll('article span');
        for (const span of articleSpans) {
          const text = span.textContent?.trim() || '';
          if (text.length > 20 &&
              !text.includes(author) &&
              !text.includes('ago') &&
              !text.includes('likes') &&
              !text.includes('Follow')) {
            caption = text;
            break;
          }
        }
      }

      console.log(`Instagram Cloud: Extracted caption: "${caption.substring(0, 100)}${caption.length > 100 ? '...' : ''}"`);


      // --- Timestamp ---
      // Extract the timestamp
      let timestamp = '';
      const timeElement = element.querySelector('time');
      if (timeElement) {
        timestamp = timeElement.dateTime || timeElement.getAttribute('datetime') || '';
      }

      // --- Interactions ---
      // Extract likes and comments
      const likesSpan = element.querySelector<HTMLSpanElement>(SELECTORS.LIKES_LINK_SPAN);
      const commentsSpan = element.querySelector<HTMLSpanElement>(SELECTORS.COMMENTS_LINK_SPAN);

      const likeCount = this.parseCount(likesSpan?.textContent);
      const commentCount = this.parseCount(commentsSpan?.textContent);

      console.log(`Instagram Cloud: Likes: ${likeCount}, Comments: ${commentCount}`);

      // --- Media URLs ---
      // Extract image URLs (we'll only send the URLs to the backend)
      const mediaItems: MediaItem[] = [];

      // Find all image elements
      const imgElements = element.querySelectorAll<HTMLImageElement>(SELECTORS.IMAGE);
      console.log(`Instagram Cloud: Found ${imgElements.length} potential image elements`);

      // Process each image and convert to base64
      for (const img of imgElements) {
        if (img.src &&
            !img.src.includes('data:image') &&
            !img.alt?.includes("profile picture") &&
            img.src !== authorAvatar) {

          try {
            // Convert image to base64 to avoid CORS issues
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (ctx) {
              canvas.width = img.naturalWidth || img.width;
              canvas.height = img.naturalHeight || img.height;

              // Draw the image onto the canvas
              ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

              // Convert canvas to data URL
              const base64Url = canvas.toDataURL('image/jpeg', 0.9);

              mediaItems.push({
                type: 'image',
                url: base64Url, // Use base64 data URL instead of Instagram URL
                originalUrl: img.src, // Keep original URL for reference
                alt: img.alt || 'Instagram image'
              });

              console.log(`Instagram Cloud: Successfully converted image to base64 (${base64Url.length} chars)`);
            } else {
              // Fallback to original URL if canvas fails
              mediaItems.push({
                type: 'image',
                url: img.src,
                alt: img.alt || 'Instagram image'
              });
              console.log(`Instagram Cloud: Canvas context failed, using original URL: ${img.src.substring(0, 50)}...`);
            }
          } catch (error) {
            // Handle canvas security errors (tainted canvas)
            if (error instanceof Error && error.message.includes('Tainted canvases may not be exported')) {
              console.log(`Instagram Cloud: Canvas tainted, using original URL for image: ${img.src.substring(0, 50)}...`);
            } else {
              console.error('Instagram Cloud: Error converting image to base64:', error);
            }
            // Fallback to original URL if conversion fails
            mediaItems.push({
              type: 'image',
              url: img.src,
              alt: img.alt || 'Instagram image'
            });
          }
        }
      }

      // If no images found, try carousel items
      if (mediaItems.length === 0) {
        const carouselItems = element.querySelectorAll<HTMLImageElement>(SELECTORS.CAROUSEL_ITEM);
        console.log(`Instagram Cloud: Found ${carouselItems.length} potential carousel items`);

        for (const img of carouselItems) {
          if (img.src &&
              !img.src.includes('data:image') &&
              !img.alt?.includes("profile picture") &&
              img.src !== authorAvatar) {

            try {
              // Convert carousel image to base64 to avoid CORS issues
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');

              if (ctx) {
                canvas.width = img.naturalWidth || img.width;
                canvas.height = img.naturalHeight || img.height;

                // Draw the image onto the canvas
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                // Convert canvas to data URL
                const base64Url = canvas.toDataURL('image/jpeg', 0.9);

                mediaItems.push({
                  type: 'image',
                  url: base64Url, // Use base64 data URL instead of Instagram URL
                  originalUrl: img.src, // Keep original URL for reference
                  alt: img.alt || 'Instagram carousel image'
                });

                console.log(`Instagram Cloud: Successfully converted carousel image to base64 (${base64Url.length} chars)`);
              } else {
                // Fallback to original URL if canvas fails
                mediaItems.push({
                  type: 'image',
                  url: img.src,
                  alt: img.alt || 'Instagram carousel image'
                });
                console.log(`Instagram Cloud: Canvas context failed for carousel, using original URL: ${img.src.substring(0, 50)}...`);
              }
            } catch (error) {
              console.error('Instagram Cloud: Error converting carousel image to base64:', error);
              // Fallback to original URL if conversion fails
              mediaItems.push({
                type: 'image',
                url: img.src,
                alt: img.alt || 'Instagram carousel image'
              });
            }
          }
        }
      }

      console.log(`Instagram Cloud: Extracted ${mediaItems.length} media items`);

      // Create the post object with minimal data
      const post: Post = {
        id: shortcode,
        platform: 'Instagram',
        author,
        authorName: author,
        authorUrl,
        authorAvatar,
        textContent: caption,
        content: caption,
        permalink: postUrl,
        timestamp,
        savedAt: new Date().toISOString(),
        media: mediaItems,
        stats: {
          likes: likeCount,
          comments: commentCount,
          shares: 0,
          views: 0
        }
      };

      return {
        success: true,
        post
      };
    } catch (error) {
      console.error('Instagram Cloud: Error extracting post data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Extract image URLs from a post element or post data
   * This is a simplified version since actual image processing happens on the server
   */
  async extractImageUrls(source: Post | HTMLElement, options?: ExtractionOptions): Promise<any> {
    // This method is simplified since image processing happens on the server
    return {
      success: true,
      images: source instanceof HTMLElement ? [] : source.media || []
    };
  }

  /**
   * Parse a count string (e.g., "1,234 likes" or "1.2K comments")
   * @param countText The count text to parse
   * @returns The parsed count as a number
   */
  private parseCount(countText: string | undefined | null): number {
    if (!countText) return 0;

    // Remove non-numeric characters except for K, M, and decimal points
    const cleanText = countText.replace(/[^0-9KkMm.]/g, '');

    // Parse the number
    if (cleanText.match(/[Kk]$/)) {
      return parseFloat(cleanText) * 1000;
    } else if (cleanText.match(/[Mm]$/)) {
      return parseFloat(cleanText) * 1000000;
    } else {
      return parseFloat(cleanText) || 0;
    }
  }

  /**
   * Save post data to local storage
   * @param post The post data to save
   * @returns Promise<boolean> indicating success or failure
   */
  async saveToLocal(post: Post): Promise<boolean> {
    try {
      // Use the existing storage service
      await savePost(post);
      return true;
    } catch (error) {
      console.error('Instagram Cloud: Error saving post to local storage:', error);
      return false;
    }
  }

  /**
   * Upload post to cloud
   * @param post The post data to upload
   * @returns Promise<boolean> indicating success or failure
   */
  async uploadToCloud(post: Post, token?: string): Promise<boolean> {
    try {
      if (!token) {
        token = await authService.getToken();
        if (!token) {
          console.error('Instagram Cloud: No authentication token available');
          return false;
        }
      }

      // Define backend URL - use the unified endpoint for queue processing
      const API_URL = 'https://api.notely.social';
      const apiUrl = `${API_URL}/api/posts`;

      console.log(`Instagram Cloud: Uploading post to ${apiUrl}`);

      // Prepare the post for upload - only send necessary data
      const postToUpload = {
        originalPostId: post.id,
        platform: post.platform,
        authorName: post.authorName || post.author,
        authorHandle: post.authorHandle,
        authorAvatar: post.authorAvatar,
        content: post.content || post.textContent,
        timestamp: post.timestamp,
        permalink: post.permalink,
        media: post.media,
        interactions: {
          likes: post.stats?.likes || 0,
          comments: post.stats?.comments || 0
        }
      };

      // Send the post to the backend
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(postToUpload)
      });

      if (response.ok) {
        console.log(`Instagram Cloud: Successfully uploaded post ${post.id} to cloud.`);
        return true;
      } else if (response.status === 409) {
        console.log(`Instagram Cloud: Post ${post.id} already exists in cloud.`);
        return true; // Consider this a success
      } else {
        const errorData = await response.json().catch(() => ({ message: `Cloud upload failed with status: ${response.status}` }));
        console.error(`Instagram Cloud: Failed to upload post ${post.id} to cloud. Status: ${response.status}`, errorData);
        return false;
      }
    } catch (error) {
      console.error('Instagram Cloud: Error uploading post to cloud:', error);
      return false;
    }
  }

  /**
   * Process and save a post
   * @param post The post to save
   * @param options Options for saving
   * @returns SaveResult containing success status and error message if applicable
   */
  async processAndSavePost(post: Post, options: SaveOptions = {}): Promise<SaveResult> {
    try {
      console.log(`Instagram Cloud: Processing and saving post ${post.id}`);

      // Always save to local storage first
      let savedLocally = false;
      if (options.saveToLocal !== false) {
        savedLocally = await this.saveToLocal(post);
        console.log(`Instagram Cloud: Post ${post.id} saved locally: ${savedLocally}`);
      }

      // Upload to cloud if requested
      if (options.uploadToCloud) {
        const token = await authService.getToken();

        if (!token) {
          console.log('Instagram Cloud: No authentication token available. Post saved locally only.');
          return {
            success: true, // Still consider this a success since we saved locally
            postId: post.id,
            savedLocally: savedLocally,
            uploadedToCloud: false,
            error: 'Post saved locally only. Sign in to enable cloud backup.'
          };
        }

        const cloudSuccess = await this.uploadToCloud(post, token);
        return {
          success: true,
          postId: post.id,
          savedLocally: savedLocally,
          uploadedToCloud: cloudSuccess,
          error: cloudSuccess ? undefined : 'Failed to upload to cloud, but post was saved locally'
        };
      }

      return {
        success: true,
        postId: post.id,
        savedLocally: savedLocally,
        uploadedToCloud: false
      };
    } catch (error) {
      console.error('Instagram Cloud: Error processing and saving post:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}
