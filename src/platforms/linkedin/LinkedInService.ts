/**
 * LinkedIn Service
 *
 * Handles LinkedIn-specific post extraction and saving logic.
 */

import { Post, Platform, MediaItem } from '../../types';
import { BasePlatformService } from '../base/BasePlatformService';
import { ExtractionOptions, SaveOptions, ExtractionResult, SaveResult, ImageExtractionResult } from '../base/types';
import { savePost } from '../../storage';

export class LinkedInService extends BasePlatformService {
  constructor() {
    super('LinkedIn');
  }

  /**
   * Extract post data from a LinkedIn post element
   * @param element The post element
   * @param options Options for extraction
   * @returns ExtractionResult containing the extracted post data or an error
   */
  async extractPostData(element: HTMLElement, options?: ExtractionOptions): Promise<ExtractionResult> {
    try {
      console.log('[LinkedIn] Starting post extraction');

      // LinkedIn-specific extraction logic with multiple selector fallbacks

      // Extract author information with multiple selector options
      let authorElement = element.querySelector('.update-components-actor__name');
      if (!authorElement) {
        authorElement = element.querySelector('.feed-shared-actor__name');
      }
      if (!authorElement) {
        authorElement = element.querySelector('.update-components-actor__title');
      }

      // Clean up the author name to remove duplicates and extra content
      let authorName = authorElement?.textContent?.trim() || '';

      // Remove common LinkedIn UI elements and duplicates
      authorName = authorName
        .replace(/\s*•\s*Following.*$/i, '') // Remove "• Following" and everything after
        .replace(/\s*Verified.*$/i, '') // Remove "Verified" and everything after
        .replace(/\s*\n.*$/s, '') // Remove everything after first newline
        .replace(/(.+?)\1+/g, '$1') // Remove duplicates (e.g., "John SmithJohn Smith" -> "John Smith")
        .trim();

      console.log('[LinkedIn] Extracted author name:', authorName);

      // Extract author avatar with very specific selectors based on actual LinkedIn structure
      let authorAvatarElement = null;
      let selectorUsed = 'none';

      // Try the most specific selector first - the actual author avatar
      authorAvatarElement = element.querySelector('.update-components-actor__avatar-image');
      if (authorAvatarElement) selectorUsed = '.update-components-actor__avatar-image';

      if (!authorAvatarElement) {
        authorAvatarElement = element.querySelector('.update-components-actor__image-container img');
        if (authorAvatarElement) selectorUsed = '.update-components-actor__image-container img';
      }

      if (!authorAvatarElement) {
        authorAvatarElement = element.querySelector('.feed-shared-actor__avatar img');
        if (authorAvatarElement) selectorUsed = '.feed-shared-actor__avatar img';
      }

      if (!authorAvatarElement) {
        // Look specifically in the actor avatar container
        const avatarContainer = element.querySelector('.js-update-components-actor__avatar, .update-components-actor__avatar');
        if (avatarContainer) {
          authorAvatarElement = avatarContainer.querySelector('img');
          if (authorAvatarElement) selectorUsed = 'avatar container img';
        }
      }

      if (!authorAvatarElement) {
        // Look for images with specific alt text patterns that indicate author avatar
        const actorArea = element.querySelector('.update-components-actor, .feed-shared-actor');
        if (actorArea) {
          // Look for images with alt text containing "View [name]'s" which is the author avatar pattern
          authorAvatarElement = actorArea.querySelector('img[alt*="View "][alt*="\'s"]');
          if (authorAvatarElement) selectorUsed = 'alt text View [name]\'s';

          if (!authorAvatarElement) {
            // Look for images with profile-related alt text
            authorAvatarElement = actorArea.querySelector('img[alt*="profile"], img[alt*="avatar"]');
            if (authorAvatarElement) selectorUsed = 'alt text profile/avatar';
          }
        }
      }

      const authorAvatar = authorAvatarElement instanceof HTMLImageElement ? authorAvatarElement.src : '';
      console.log('[LinkedIn] Extracted author avatar:', authorAvatar ? `Found using: ${selectorUsed}` : 'Not found');
      if (authorAvatarElement && authorAvatarElement instanceof HTMLImageElement) {
        console.log('[LinkedIn] Avatar alt text:', authorAvatarElement.alt);
        console.log('[LinkedIn] Avatar src:', authorAvatarElement.src);
      }

      // Extract post content with multiple selector options
      let contentElement = element.querySelector('.update-components-text');
      if (!contentElement) {
        contentElement = element.querySelector('.feed-shared-update-v2__description');
      }
      if (!contentElement) {
        contentElement = element.querySelector('.feed-shared-text');
      }

      const textContent = contentElement?.textContent?.trim() || '';
      console.log('[LinkedIn] Extracted text content:', textContent ? 'Found' : 'Not found');

      // Extract post URL with multiple selector options
      let permalinkElement = element.querySelector('.update-components-update-v2__permalink-link');
      if (!permalinkElement) {
        permalinkElement = element.querySelector('.feed-shared-update-v2__permalink');
      }
      if (!permalinkElement) {
        permalinkElement = element.querySelector('a[data-control-name="update_permalink"]');
      }

      let permalink = permalinkElement instanceof HTMLAnchorElement ? permalinkElement.href : '';

      // If no permalink found, try to construct one from the post ID
      if (!permalink) {
        const postIdElement = element.querySelector('[data-id]');
        const postId = postIdElement?.getAttribute('data-id');
        if (postId) {
          permalink = `https://www.linkedin.com/feed/update/${postId}/`;
        }
      }

      console.log('[LinkedIn] Extracted permalink:', permalink ? 'Found' : 'Not found');

      // Generate a unique ID
      const id = `linkedin-${Date.now()}-${Math.floor(Math.random() * 10000)}`;

      const post: Post = {
        id,
        platform: 'LinkedIn',
        author: authorName,
        authorName,
        authorUrl: `https://www.linkedin.com/in/${authorName.toLowerCase().replace(/\s+/g, '-')}`,
        authorAvatar,
        textContent,
        content: textContent,
        permalink,
        savedAt: new Date().toISOString(),
        media: [],
        stats: {
          likes: 0,
          comments: 0,
          shares: 0,
          views: 0
        },
        categories: [],
        tags: []
      };

      // Extract images if requested
      if (options?.includeImages) {
        const imageResult = await this.extractImageUrls(element, options);
        if (imageResult.success && imageResult.images) {
          post.media = imageResult.images;
        }
      }

      console.log('[LinkedIn] Post extraction completed successfully');
      return {
        success: true,
        post
      };
    } catch (error) {
      console.error('[LinkedIn] Error extracting post data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Extract image URLs from a LinkedIn post element or post data
   * @param source The post element or post data
   * @param options Options for extraction
   * @returns ImageExtractionResult containing the extracted images or an error
   */
  async extractImageUrls(source: Post | HTMLElement, options?: ExtractionOptions): Promise<ImageExtractionResult> {
    try {
      console.log('[LinkedIn] Starting image extraction');
      const images: MediaItem[] = [];
      const uniqueUrls = new Set<string>(); // To avoid duplicate images

      if (source instanceof HTMLElement) {
        // LinkedIn-specific image extraction logic from DOM with multiple selector fallbacks

        // Try multiple selectors for images
        const selectors = [
          '.update-components-image img',
          '.feed-shared-image img',
          '.feed-shared-update-v2__content img',
          '.feed-shared-article__image img',
          '.feed-shared-external-video__image img',
          '.feed-shared-mini-article__image img',
          '.feed-shared-update-v2 img[data-test-id="image"]',
          '.artdeco-card img[width]'
        ];

        // Try each selector
        for (const selector of selectors) {
          const imgElements = source.querySelectorAll(selector);
          console.log(`[LinkedIn] Found ${imgElements.length} images with selector: ${selector}`);

          imgElements.forEach((img) => {
            if (img instanceof HTMLImageElement && img.src) {
              // Skip profile images, icons, and small images
              if (!img.src.includes('profile-image') &&
                  !img.src.includes('icon') &&
                  !img.src.includes('emoji') &&
                  (img.width > 100 || img.height > 100)) {

                // Skip if we already have this URL
                if (!uniqueUrls.has(img.src)) {
                  uniqueUrls.add(img.src);
                  images.push({
                    type: 'image',
                    url: img.src,
                    alt: img.alt || 'LinkedIn image',
                    width: img.width || undefined,
                    height: img.height || undefined
                  });
                }
              }
            }
          });

          // If we found images with this selector, no need to try others
          if (images.length > 0) {
            break;
          }
        }

        // Extract video thumbnails with multiple selector fallbacks
        const videoSelectors = [
          '.update-components-video',
          '.feed-shared-video',
          '.feed-shared-linkedin-video',
          '.feed-shared-external-video'
        ];

        for (const selector of videoSelectors) {
          const videoElements = source.querySelectorAll(selector);
          console.log(`[LinkedIn] Found ${videoElements.length} videos with selector: ${selector}`);

          videoElements.forEach((video) => {
            const thumbnail = video.querySelector('img');
            if (thumbnail instanceof HTMLImageElement && thumbnail.src) {
              // Skip if we already have this URL
              if (!uniqueUrls.has(thumbnail.src)) {
                uniqueUrls.add(thumbnail.src);
                images.push({
                  type: 'image',
                  url: thumbnail.src,
                  alt: thumbnail.alt || 'LinkedIn video thumbnail',
                  width: thumbnail.width || undefined,
                  height: thumbnail.height || undefined
                });
              }
            }
          });

          // If we found video thumbnails with this selector, no need to try others
          if (images.length > 0) {
            break;
          }
        }
      } else {
        // Extract images from post data
        if (source.media) {
          images.push(...source.media);
        }
      }

      console.log(`[LinkedIn] Image extraction completed, found ${images.length} images`);
      return {
        success: true,
        images
      };
    } catch (error) {
      console.error('[LinkedIn] Error extracting images:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Save post data to local storage
   * @param post The post data to save
   * @returns Promise<boolean> indicating success or failure
   */
  async saveToLocal(post: Post): Promise<boolean> {
    try {
      await savePost(post);
      return true;
    } catch (error) {
      console.error('Error saving LinkedIn post to local storage:', error);
      return false;
    }
  }

  /**
   * Upload post data to cloud storage
   * @param post The post data to upload
   * @returns Promise<boolean> indicating success or failure
   */
  async uploadToCloud(post: Post): Promise<boolean> {
    try {
      const { syncToCloud } = await import('../../services/cloudSyncService');
      const result = await syncToCloud(post);
      return result.success;
    } catch (error) {
      console.error('Error uploading LinkedIn post to cloud:', error);
      return false;
    }
  }
}
