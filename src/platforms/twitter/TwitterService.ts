/**
 * Twitter Service
 *
 * Handles Twitter-specific post extraction and saving logic.
 */

import { Post, AnalyzedPost, CoreSubCategorySlug, MediaItem, Platform } from "../../types";
import { BasePlatformService } from '../base/BasePlatformService';
import { ExtractionOptions, ExtractionResult, ImageExtractionResult } from '../base/types';
import { savePost } from '../../storage';

export class TwitterService extends BasePlatformService {
  constructor() {
    super('X/Twitter');
  }

  /**
   * Extract post data from a Twitter tweet element
   * @param element The tweet element
   * @param options Options for extraction
   * @returns ExtractionResult containing the extracted post data or an error
   */
  // Helper method to extract and parse stat numbers (e.g., likes, replies)
  private extractStat(element: HTMLElement, selector: string): number {
    const statElement = element.querySelector(selector + ' [data-testid="app-text-transition-container"], ' + selector + ' span > span > span'); // Common patterns for stat numbers
    let text = statElement?.textContent?.trim();

    if (!text) {
        // Fallback for view counts that might be directly in a span after an SVG, or similar structures
        const directSpan = element.querySelector(selector + ' span[aria-hidden="true"]');
        if (directSpan) text = directSpan.textContent?.trim();
        else {
            const groupSpan = element.querySelector(selector + ' div[role="group"] span[data-testid="app-text-transition-container"] span');
            if (groupSpan) text = groupSpan.textContent?.trim();
        }
    }
    
    if (!text) return 0;

    const num = parseFloat(text.replace(/,/g, '')); // Remove commas before parsing
    if (isNaN(num)) return 0;

    if (text.toUpperCase().endsWith('K')) return num * 1000;
    if (text.toUpperCase().endsWith('M')) return num * 1000000;
    if (text.toUpperCase().endsWith('B')) return num * 1000000000;
    return num;
  }

  // Helper method to extract video sources
  private extractVideoSources(element: HTMLElement): MediaItem[] {
    const videos: MediaItem[] = [];
    const videoElements = element.querySelectorAll('video');
    videoElements.forEach(videoEl => {
      let src = videoEl.getAttribute('src');
      if (!src) {
        const sourceEl = videoEl.querySelector('source');
        if (sourceEl) {
          src = sourceEl.getAttribute('src');
        }
      }
      if (src) {
        videos.push({ type: 'video', url: src, alt: 'Video content' });
      }
    });
    return videos;
  }



  async extractPostData(element: HTMLElement, options?: ExtractionOptions): Promise<ExtractionResult> {
    try {
      const savedAt = new Date().toISOString();

      // --- Author Info ---
      let authorNameElement = element.querySelector('[data-testid="User-Name"] span span');
      if (!authorNameElement) authorNameElement = element.querySelector('a[role="link"] div[dir="auto"] span span');
      let authorHandleElement = element.querySelector('[data-testid="User-Name"] div[dir="ltr"] span');
      if (!authorHandleElement) authorHandleElement = element.querySelector('a[role="link"] div[dir="ltr"] span');
      let authorAvatarElement = element.querySelector('[data-testid="Tweet-User-Avatar"] img');
      if (!authorAvatarElement) authorAvatarElement = element.querySelector('a[role="link"] img[src*="twimg.com"]');

      const authorName = authorNameElement?.textContent?.trim() || '';
      const initialAuthorHandle = authorHandleElement?.textContent?.trim().replace('@', '') || '';
      const authorAvatar = authorAvatarElement?.getAttribute('src') || '';
      
      console.log('[Notely Debug] Initial Extracted Author Name:', authorName);
      console.log('[Notely Debug] Initial Extracted Author Handle:', initialAuthorHandle);
      console.log('[Notely Debug] Initial Extracted Author Avatar:', authorAvatar?.substring(0,100));

      // --- Tweet Content ---
      let tweetTextElement = element.querySelector('[data-testid="tweetText"]');
      if (!tweetTextElement) tweetTextElement = element.querySelector('div[lang][dir="auto"]:not([data-testid])'); // More specific to avoid grabbing quoted tweet text prematurely
      
      let content = '';
      if (tweetTextElement) {
          const clonedTextElement = tweetTextElement.cloneNode(true) as HTMLElement;
          clonedTextElement.querySelectorAll('[role="button"]').forEach(btn => btn.remove()); // Remove "Show more"
          content = clonedTextElement.textContent?.trim() || '';
      }
      if (!content) { // Fallback for some quoted tweet structures or simple text displays
          const simpleText = element.querySelector('article div[lang][dir]');
          if(simpleText && simpleText.closest('article[data-testid="tweet"]')?.isSameNode(element.closest('article[data-testid="tweet"]'))) {
            content = simpleText.textContent?.trim() || '';
          }
      }
      console.log('[Notely Debug] Extracted tweet content:', content.substring(0, 70) + (content.length > 70 ? '...' : ''));

      // --- Permalink ---
      console.log('[Notely Debug] Starting permalink extraction...');
      const timeElement = element.querySelector('time');
      let permalink = timeElement?.closest('a')?.href || '';
      console.log('[Notely Debug] Permalink from time element:', permalink);

      if (!permalink) {
        const selectorsToTry = [
          'a[href*="/status/"][aria-describedby][role="link"][target="_blank"]', // Common for main tweet link
          'a[href*="/status/"][aria-label*="ago"][role="link"]', // Links with time information
          'article[data-testid="tweet"] a[href*="/status/"]',
          'div[data-testid="tweetPhoto"] a[href*="/status/"]'
        ];
        for (const selector of selectorsToTry) {
          const linkElement = element.querySelector(selector) as HTMLAnchorElement | null;
          if (linkElement?.href && linkElement.href.includes('/status/')) {
            // Ensure it's the permalink of the current tweet, not a quoted one by checking proximity or context
            if (linkElement.closest('article[data-testid="tweet"]')?.isSameNode(element.closest('article[data-testid="tweet"]'))) {
                 permalink = linkElement.href;
                 console.log(`[Notely Debug] Permalink found via fallback selector '${selector}':`, permalink);
                 break;
            }
          }
        }
      }
      
      if (!permalink && initialAuthorHandle) {
        const pseudoId = `pseudo-${Date.now()}`;
        permalink = `https://twitter.com/${initialAuthorHandle}/status/${pseudoId}`;
        console.log('[Notely Debug] Constructed fallback permalink:', permalink);
      } else if (!permalink) {
        console.error('[Notely CRITICAL] PERMALINK COULD NOT BE DETERMINED.');
        return { success: false, error: 'Failed to extract permalink.' };
      }

      // --- Author Handle and URL from Permalink ---
      let authorHandleFromPermalink = '';
      try {
        const url = new URL(permalink);
        const pathParts = url.pathname.split('/');
        if (pathParts.length >= 3 && pathParts[2] === 'status') {
          authorHandleFromPermalink = pathParts[1];
        }
      } catch { /* Ignore parsing errors */ }
      
      const finalAuthorHandle = authorHandleFromPermalink || initialAuthorHandle;
      const finalAuthorUrl = finalAuthorHandle ? `https://twitter.com/${finalAuthorHandle}` : '';
      console.log('[Notely Debug] Final Author Handle:', finalAuthorHandle, '(from permalink:', authorHandleFromPermalink, ', initial:', initialAuthorHandle, ')');
      console.log('[Notely Debug] Final Author URL:', finalAuthorUrl);

      // --- Tweet ID from Permalink ---
      let id = '';
      const idMatch = permalink.match(/status\/(\d+)/);
      if (idMatch && idMatch[1]) {
        id = idMatch[1];
      } else if (permalink.includes('/status/pseudo-')) {
        id = permalink.substring(permalink.lastIndexOf('/') + 1);
      }
      console.log('[Notely Debug] Extracted ID from permalink:', id);

      if (!id) {
        console.error('[Notely CRITICAL] TWEET ID COULD NOT BE EXTRACTED from permalink:', permalink);
        return { success: false, error: 'Failed to extract tweet ID from permalink: ' + permalink };
      }

      // --- Timestamp ---
      const timestamp = timeElement?.getAttribute('datetime') || savedAt;

      // --- Stats ---
      const stats = {
        likes: this.extractStat(element, '[data-testid="like"]'),
        comments: this.extractStat(element, '[data-testid="reply"]'),
        shares: this.extractStat(element, '[data-testid="retweet"]'),
        views: this.extractStat(element, 'a[href$="/analytics"], div[role="group"][aria-label*="views"]'),
      };
      console.log('[Notely Debug] Extracted Stats:', stats);

      // --- Media ---
      const imageResult = await this.extractImageUrls(element);
      let mediaItems: MediaItem[] = imageResult.images || [];
      const videoItems = this.extractVideoSources(element);
      mediaItems = mediaItems.concat(videoItems);
      console.log('[Notely Debug] Extracted Media Items:', mediaItems.length);

      // --- Construct Post ---
      const postData: Post = {
        id: id,
        platform: this.platform, // this.platform is 'X/Twitter', correctly typed from BasePlatformService
        author: authorName || finalAuthorHandle, // Simplified author string
        authorName: authorName,
        authorHandle: finalAuthorHandle,
        authorUrl: finalAuthorUrl,
        authorAvatar: authorAvatar,
        content: content, // Main text content
        textContent: content, // Potentially enriched later
        title: content.substring(0, 100), // Basic title from content
        createdAt: timestamp || new Date(0).toISOString(), // Original post timestamp
        savedAt: savedAt,
        permalink: permalink,
        media: [], // Initialize as empty, will be populated by extractImageUrls
        stats: stats,
        categories: [], // Initialize as empty array
        tags: [],       // Initialize as empty array
      };

      // Extract images if requested
      if (options?.includeImages !== false) {
        const imageResult = await this.extractImageUrls(element);
        if (imageResult.success && imageResult.images) {
          postData.media = imageResult.images;
        }
      }

      return {
        success: true,
        post: postData
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Extract image URLs from a Twitter tweet element.
   * Implements abstract method from BasePlatformService.
   * @param element The tweet HTMLElement
   * @param options Options for extraction (currently unused by this implementation)
   * @returns ImageExtractionResult containing the extracted images or an error
   */
  async extractImageUrls(element: HTMLElement, _options?: ExtractionOptions): Promise<ImageExtractionResult> {
    try {
      const images: MediaItem[] = [];
      const uniqueUrls = new Set<string>();

      // Twitter-specific image extraction logic from DOM
      // Find images in the tweet
      const mediaContainer = element.querySelector('[data-testid="tweetPhoto"], [data-testid="videoPlayer"]');
      if (mediaContainer) {
        // Handle images
        const imgElements = mediaContainer.querySelectorAll('img[src*="twimg.com"]');
        imgElements.forEach((img) => {
          if (img instanceof HTMLImageElement && img.src) {
            // Skip small images (likely avatars or icons)
            if (img.width > 100 && img.height > 100) {
              // Get the highest quality version of the image
              let imageUrl = img.src;
              // Replace Twitter's image sizing parameters to get the original size
              imageUrl = imageUrl.replace(/&name=\w+/, '&name=orig');

              if (!uniqueUrls.has(imageUrl)) {
                uniqueUrls.add(imageUrl);
                images.push({
                  type: 'image',
                  url: imageUrl,
                  alt: img.alt || 'Twitter image'
                });
              }
            }
          }
        });

        // Handle videos
        const videoElements = mediaContainer.querySelectorAll('video');
        videoElements.forEach((video) => {
          if (video instanceof HTMLVideoElement && video.src) {
            if (!uniqueUrls.has(video.src)) {
              uniqueUrls.add(video.src);
              images.push({
                type: 'video',
                url: video.src,
                alt: 'Twitter video'
              });
            }
          }
        });

        // If no video source found directly, try to get the poster image
        if (videoElements.length > 0 && images.length === 0) {
          const poster = videoElements[0].poster;
          if (poster && !uniqueUrls.has(poster)) {
            uniqueUrls.add(poster);
            images.push({
              type: 'image',
              url: poster,
              alt: 'Twitter video thumbnail'
            });
          }
        }
      }

      // Check for GIFs
      const gifContainer = element.querySelector('[data-testid="tweetGif"]');
      if (gifContainer) {
        const gifImg = gifContainer.querySelector('img[src*="twimg.com"]');
        if (gifImg instanceof HTMLImageElement && gifImg.src) {
          let gifUrl = gifImg.src;
          // Try to get the highest quality version
          gifUrl = gifUrl.replace(/&name=\w+/, '&name=orig');

          if (!uniqueUrls.has(gifUrl)) {
            uniqueUrls.add(gifUrl);
            images.push({
              type: 'image',
              url: gifUrl,
              alt: gifImg.alt || 'Twitter GIF'
            });
          }
        }
      }

      // Check for card images (e.g., link previews)
      const cardContainer = element.querySelector('[data-testid="card.wrapper"]');
      if (cardContainer) {
        const cardImg = cardContainer.querySelector('img[src*="twimg.com"]');
        if (cardImg instanceof HTMLImageElement && cardImg.src) {
          // Skip small images (likely avatars or icons)
          if (cardImg.width > 100 && cardImg.height > 100) {
            let cardImgUrl = cardImg.src;
            // Try to get the highest quality version
            cardImgUrl = cardImgUrl.replace(/&name=\w+/, '&name=orig');

            if (!uniqueUrls.has(cardImgUrl)) {
              uniqueUrls.add(cardImgUrl);
              images.push({
                type: 'image',
                url: cardImgUrl,
                alt: cardImg.alt || 'Twitter card image'
              });
            }
          }
        }
      }
      // The 'else' block that handled 'source' as a Post object has been removed.
      // The method now only processes an HTMLElement.

      return {
        success: true,
        images
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Save post data to local storage
   * @param post The post data to save
   * @returns Promise<boolean> indicating success or failure
   */
  async saveToLocal(post: Post): Promise<boolean> {
    try {
      await savePost(post);
      return true;
    } catch (error) {
      console.error('Error saving Twitter post to local storage:', error);
      return false;
    }
  }

  /**
   * Upload post data to cloud storage
   * @param post The post data to upload
   * @returns Promise<boolean> indicating success or failure
   */
  async uploadToCloud(post: Post): Promise<boolean> {
    try {
      const { syncToCloud } = await import('../../services/cloudSyncService');
      
      const postForUpload: AnalyzedPost = {
        ...post,
        categories: (post.categories as CoreSubCategorySlug[] || []),
        tags: post.tags || [],
        // Initialize AnalyzedPost specific fields not present on Post
        snapNote: null, 
        embeddingVector: [], 
        analyzedAt: new Date().toISOString(), 
        inSight: null,
        fastTake: null,
        contentIdeas: null,
      };

      const result = await syncToCloud(postForUpload);
      return result.success;
    } catch (error) {
      console.error('Error uploading Twitter post to cloud:', error);
      return false;
    }
  }
}
