// Test script to verify settings functionality
// Run this in the browser console on the settings page

console.log('=== Testing Settings Functionality ===');

// Test 1: Check if settings are loaded correctly
async function testSettingsLoad() {
  console.log('\n1. Testing settings load...');
  
  try {
    const localSettings = await new Promise(resolve => {
      chrome.storage.local.get(['saveMediaToCloud', 'showMindstreamTips', 'platformIntegrations'], resolve);
    });
    
    const syncSettings = await new Promise(resolve => {
      chrome.storage.sync.get(['locale'], resolve);
    });
    
    console.log('✅ Local settings loaded:', localSettings);
    console.log('✅ Sync settings loaded:', syncSettings);
    
    return { localSettings, syncSettings };
  } catch (error) {
    console.error('❌ Error loading settings:', error);
    return null;
  }
}

// Test 2: Test platform toggle functionality
async function testPlatformToggle() {
  console.log('\n2. Testing platform toggle...');
  
  try {
    // Get current platform integrations
    const current = await new Promise(resolve => {
      chrome.storage.local.get(['platformIntegrations'], resolve);
    });
    
    console.log('Current platform integrations:', current.platformIntegrations);
    
    // Toggle X/Twitter
    const newIntegrations = {
      ...current.platformIntegrations,
      'X/Twitter': !current.platformIntegrations['X/Twitter']
    };
    
    // Save the change
    await new Promise(resolve => {
      chrome.storage.local.set({ platformIntegrations: newIntegrations }, resolve);
    });
    
    // Verify the change
    const updated = await new Promise(resolve => {
      chrome.storage.local.get(['platformIntegrations'], resolve);
    });
    
    console.log('✅ Platform integrations updated:', updated.platformIntegrations);
    
    // Restore original state
    await new Promise(resolve => {
      chrome.storage.local.set({ platformIntegrations: current.platformIntegrations }, resolve);
    });
    
    console.log('✅ Platform integrations restored');
    
    return true;
  } catch (error) {
    console.error('❌ Error testing platform toggle:', error);
    return false;
  }
}

// Test 3: Test language change functionality
async function testLanguageChange() {
  console.log('\n3. Testing language change...');
  
  try {
    // Get current locale
    const current = await new Promise(resolve => {
      chrome.storage.sync.get(['locale'], resolve);
    });
    
    console.log('Current locale:', current.locale);
    
    // Change to a different locale
    const newLocale = current.locale === 'en' ? 'tr' : 'en';
    
    await new Promise(resolve => {
      chrome.storage.sync.set({ locale: newLocale }, resolve);
    });
    
    // Verify the change
    const updated = await new Promise(resolve => {
      chrome.storage.sync.get(['locale'], resolve);
    });
    
    console.log('✅ Locale updated:', updated.locale);
    
    // Restore original locale
    await new Promise(resolve => {
      chrome.storage.sync.set({ locale: current.locale }, resolve);
    });
    
    console.log('✅ Locale restored');
    
    return true;
  } catch (error) {
    console.error('❌ Error testing language change:', error);
    return false;
  }
}

// Test 4: Test storage persistence
async function testStoragePersistence() {
  console.log('\n4. Testing storage persistence...');
  
  try {
    const testData = {
      saveMediaToCloud: false,
      showMindstreamTips: false,
      platformIntegrations: {
        'X/Twitter': false,
        'LinkedIn': true,
        'Reddit': false,
        'Instagram': true,
        'pinterest': false,
        'Web': true
      }
    };
    
    // Save test data
    await new Promise(resolve => {
      chrome.storage.local.set(testData, resolve);
    });
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Retrieve and verify
    const retrieved = await new Promise(resolve => {
      chrome.storage.local.get(Object.keys(testData), resolve);
    });
    
    const isMatch = JSON.stringify(testData) === JSON.stringify(retrieved);
    
    if (isMatch) {
      console.log('✅ Storage persistence test passed');
    } else {
      console.error('❌ Storage persistence test failed');
      console.log('Expected:', testData);
      console.log('Retrieved:', retrieved);
    }
    
    return isMatch;
  } catch (error) {
    console.error('❌ Error testing storage persistence:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('Starting comprehensive settings tests...\n');
  
  const results = {
    settingsLoad: await testSettingsLoad(),
    platformToggle: await testPlatformToggle(),
    languageChange: await testLanguageChange(),
    storagePersistence: await testStoragePersistence()
  };
  
  console.log('\n=== Test Results ===');
  console.log('Settings Load:', results.settingsLoad ? '✅ PASS' : '❌ FAIL');
  console.log('Platform Toggle:', results.platformToggle ? '✅ PASS' : '❌ FAIL');
  console.log('Language Change:', results.languageChange ? '✅ PASS' : '❌ FAIL');
  console.log('Storage Persistence:', results.storagePersistence ? '✅ PASS' : '❌ FAIL');
  
  const allPassed = Object.values(results).every(result => result);
  console.log('\nOverall Result:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  return results;
}

// Make functions available globally for manual testing
window.testSettings = {
  runAllTests,
  testSettingsLoad,
  testPlatformToggle,
  testLanguageChange,
  testStoragePersistence
};

console.log('Test functions available at window.testSettings');
console.log('Run window.testSettings.runAllTests() to test all functionality');
