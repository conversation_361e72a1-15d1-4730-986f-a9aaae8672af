{"manifest_version": 3, "name": "Notely", "version": "0.1.0", "description": "Save social media posts for later viewing.", "permissions": ["storage", "scripting", "activeTab", "tabs", "identity", "contextMenus"], "oauth2": {"client_id": "952116637409-66fthi1eab2ho5b2dknk2t31ne5cjbva.apps.googleusercontent.com", "scopes": ["openid", "email", "profile"]}, "host_permissions": ["*://*.linkedin.com/*", "*://*.fbcdn.net/*", "*://*.cdninstagram.com/*", "*://*.pinterest.com/*", "https://api.notely.social/*", "http://*/*", "https://*/*"], "externally_connectable": {"matches": ["https://api.notely.social/*"]}, "background": {"service_worker": "assets/background.js", "type": "module"}, "action": {"default_popup": "popup/test-image.html", "default_icon": {"16": "extension-icons/icon16.png", "32": "extension-icons/icon32.png", "48": "extension-icons/icon48.png", "128": "extension-icons/icon128.png"}}, "content_scripts": [{"matches": ["*://*.twitter.com/*", "*://x.com/*"], "js": ["assets/content.js"]}, {"matches": ["*://*.linkedin.com/*"], "js": ["assets/linkedin.js"], "run_at": "document_idle"}, {"matches": ["*://*.reddit.com/*"], "js": ["assets/reddit.js"], "run_at": "document_idle", "type": "module"}, {"matches": ["*://*.instagram.com/*"], "js": ["assets/injected/instagram.js"], "run_at": "document_start"}, {"matches": ["*://*.instagram.com/*"], "js": ["assets/content/instagram.js"], "run_at": "document_idle"}, {"matches": ["*://*.facebook.com/*"], "js": ["assets/injected/facebook.js"], "run_at": "document_start"}, {"matches": ["*://*.facebook.com/*"], "js": ["assets/content/facebook.js"], "run_at": "document_idle"}, {"matches": ["*://*.pinterest.com/*"], "js": ["assets/content/pinterest.js"], "run_at": "document_idle"}], "icons": {"16": "extension-icons/icon16.png", "32": "extension-icons/icon32.png", "48": "extension-icons/icon48.png", "128": "extension-icons/icon128.png"}, "web_accessible_resources": [{"resources": ["assets/*", "post-detail.html"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}}