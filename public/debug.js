// Debug Panel JavaScript

// Storage keys
const SERVICE_WORKER_LOGS_KEY = 'serviceWorkerDebugLogs';
const ERROR_INFOS_KEY = 'errorInfos';

// DOM Elements
const logsContainer = document.getElementById('logs-container');
const errorsContainer = document.getElementById('errors-container');
const localStorageContainer = document.getElementById('local-storage-container');
const syncStorageContainer = document.getElementById('sync-storage-container');
const debugEnabledCheckbox = document.getElementById('debug-enabled');
const autoRefreshCheckbox = document.getElementById('auto-refresh');
const storageFilterInput = document.getElementById('storage-filter');

// Tab handling
const tabs = document.querySelectorAll('.tab');
const tabContents = document.querySelectorAll('.tab-content');

tabs.forEach(tab => {
  tab.addEventListener('click', () => {
    const tabId = tab.getAttribute('data-tab');
    
    // Update active tab
    tabs.forEach(t => t.classList.remove('active'));
    tab.classList.add('active');
    
    // Show active content
    tabContents.forEach(content => {
      content.classList.remove('active');
      if (content.id === `${tabId}-tab`) {
        content.classList.add('active');
      }
    });
    
    // Load data for the selected tab
    if (tabId === 'logs') {
      loadLogs();
    } else if (tabId === 'errors') {
      loadErrors();
    } else if (tabId === 'storage') {
      loadStorage();
    }
  });
});

// Debug logs functions
async function loadLogs() {
  try {
    const result = await chrome.storage.local.get(SERVICE_WORKER_LOGS_KEY);
    const logs = result[SERVICE_WORKER_LOGS_KEY] || [];
    displayLogs(logs);
  } catch (error) {
    console.error('Failed to load logs:', error);
    logsContainer.innerHTML = `<div class="log-entry error">Error loading logs: ${error.message}</div>`;
  }
}

function displayLogs(logs) {
  if (!logs || logs.length === 0) {
    logsContainer.innerHTML = '<div class="log-entry">No logs available</div>';
    return;
  }
  
  // Get active filters
  const activeFilters = Array.from(document.querySelectorAll('.log-filter:checked'))
    .map(checkbox => checkbox.getAttribute('data-level'));
  
  // Filter logs
  const filteredLogs = logs.filter(log => activeFilters.includes(log.level));
  
  logsContainer.innerHTML = '';
  
  filteredLogs.forEach(log => {
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${log.level}`;
    
    const timestamp = new Date(log.timestamp).toLocaleTimeString();
    
    logEntry.innerHTML = `
      <span class="log-timestamp">${timestamp}</span>
      <span class="log-level">[${log.level.toUpperCase()}]</span>
      <span class="log-context">[${log.context}]</span>
      <span class="log-message">${log.message}</span>
      ${log.data ? `<div class="log-data">${JSON.stringify(log.data, null, 2)}</div>` : ''}
    `;
    
    logsContainer.appendChild(logEntry);
  });
  
  // Scroll to bottom
  logsContainer.scrollTop = logsContainer.scrollHeight;
}

// Error logs functions
async function loadErrors() {
  try {
    const result = await chrome.storage.local.get(ERROR_INFOS_KEY);
    const errors = result[ERROR_INFOS_KEY] || [];
    displayErrors(errors);
  } catch (error) {
    console.error('Failed to load errors:', error);
    errorsContainer.innerHTML = `<div class="log-entry error">Error loading error reports: ${error.message}</div>`;
  }
}

function displayErrors(errors) {
  if (!errors || errors.length === 0) {
    errorsContainer.innerHTML = '<div class="log-entry">No error reports available</div>';
    return;
  }
  
  errorsContainer.innerHTML = '';
  
  errors.forEach(error => {
    const errorEntry = document.createElement('div');
    errorEntry.className = 'log-entry error';
    
    const timestamp = new Date(error.timestamp).toLocaleString();
    
    errorEntry.innerHTML = `
      <div><strong>Time:</strong> ${timestamp}</div>
      <div><strong>Context:</strong> ${error.context}</div>
      <div><strong>Message:</strong> ${error.message}</div>
      ${error.stack ? `<div><strong>Stack:</strong><pre>${error.stack}</pre></div>` : ''}
    `;
    
    errorsContainer.appendChild(errorEntry);
  });
}

// Storage inspection functions
async function loadStorage() {
  try {
    const localData = await chrome.storage.local.get(null);
    const syncData = await chrome.storage.sync.get(null);
    
    displayStorageData(localData, localStorageContainer);
    displayStorageData(syncData, syncStorageContainer);
  } catch (error) {
    console.error('Failed to load storage data:', error);
    localStorageContainer.innerHTML = `<div class="storage-item error">Error loading storage: ${error.message}</div>`;
  }
}

function displayStorageData(data, container) {
  container.innerHTML = '';
  
  const filter = storageFilterInput.value.toLowerCase();
  const keys = Object.keys(data).filter(key => 
    !filter || key.toLowerCase().includes(filter)
  );
  
  if (keys.length === 0) {
    container.innerHTML = '<div class="storage-item">No storage items found</div>';
    return;
  }
  
  keys.forEach(key => {
    const item = document.createElement('div');
    item.className = 'storage-item';
    
    let valueDisplay;
    try {
      valueDisplay = JSON.stringify(data[key], null, 2);
    } catch (e) {
      valueDisplay = `[Error displaying value: ${e.message}]`;
    }
    
    item.innerHTML = `
      <div class="storage-key">${key}</div>
      <div class="storage-value">${valueDisplay}</div>
    `;
    
    container.appendChild(item);
  });
}

// Event listeners
document.getElementById('refresh-logs').addEventListener('click', loadLogs);
document.getElementById('refresh-errors').addEventListener('click', loadErrors);
document.getElementById('refresh-storage').addEventListener('click', loadStorage);

document.getElementById('clear-logs').addEventListener('click', async () => {
  if (confirm('Are you sure you want to clear all debug logs?')) {
    try {
      await chrome.storage.local.set({ [SERVICE_WORKER_LOGS_KEY]: [] });
      loadLogs();
    } catch (error) {
      console.error('Failed to clear logs:', error);
      alert(`Failed to clear logs: ${error.message}`);
    }
  }
});

document.getElementById('clear-errors').addEventListener('click', async () => {
  if (confirm('Are you sure you want to clear all error reports?')) {
    try {
      await chrome.storage.local.set({ [ERROR_INFOS_KEY]: [] });
      loadErrors();
    } catch (error) {
      console.error('Failed to clear errors:', error);
      alert(`Failed to clear errors: ${error.message}`);
    }
  }
});

debugEnabledCheckbox.addEventListener('change', async () => {
  try {
    await chrome.storage.local.set({ debugEnabled: debugEnabledCheckbox.checked });
  } catch (error) {
    console.error('Failed to update debug setting:', error);
    alert(`Failed to update debug setting: ${error.message}`);
  }
});

storageFilterInput.addEventListener('input', () => {
  loadStorage();
});

document.querySelectorAll('.log-filter').forEach(checkbox => {
  checkbox.addEventListener('change', () => {
    loadLogs();
  });
});

// Auto-refresh
let refreshInterval;

autoRefreshCheckbox.addEventListener('change', () => {
  if (autoRefreshCheckbox.checked) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
});

function startAutoRefresh() {
  stopAutoRefresh();
  refreshInterval = setInterval(() => {
    const activeTabId = document.querySelector('.tab.active').getAttribute('data-tab');
    if (activeTabId === 'logs') {
      loadLogs();
    } else if (activeTabId === 'errors') {
      loadErrors();
    } else if (activeTabId === 'storage') {
      loadStorage();
    }
  }, 5000);
}

function stopAutoRefresh() {
  if (refreshInterval) {
    clearInterval(refreshInterval);
    refreshInterval = null;
  }
}

// Initialize the debug enabled checkbox
async function initDebugEnabled() {
  try {
    const result = await chrome.storage.local.get('debugEnabled');
    const debugEnabled = result.debugEnabled !== undefined ? result.debugEnabled : true;
    debugEnabledCheckbox.checked = debugEnabled;
  } catch (error) {
    console.error('Failed to initialize debug enabled setting:', error);
  }
}

// Initial load
initDebugEnabled();
loadLogs();

// Start auto-refresh if checked
if (autoRefreshCheckbox.checked) {
  startAutoRefresh();
}
