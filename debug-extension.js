// Debug script to check extension status
// Run this in the browser console on chrome://extensions/

console.log('=== DEBUGGING NOTELY EXTENSION ===');

// Check if extension is loaded
chrome.management.getAll((extensions) => {
  const notelyExt = extensions.find(ext => ext.name.includes('Notely'));
  if (notelyExt) {
    console.log('✅ Notely extension found:', notelyExt);
    console.log('Extension ID:', notelyExt.id);
    console.log('Extension enabled:', notelyExt.enabled);
    console.log('Extension permissions:', notelyExt.permissions);
  } else {
    console.log('❌ Notely extension not found');
  }
});

// Check context menus
chrome.contextMenus.removeAll(() => {
  console.log('Context menus cleared');
  
  // Try to create a simple context menu
  chrome.contextMenus.create({
    id: 'test-menu',
    title: 'Test Menu Item',
    contexts: ['page']
  }, () => {
    if (chrome.runtime.lastError) {
      console.error('❌ Error creating context menu:', chrome.runtime.lastError);
    } else {
      console.log('✅ Test context menu created successfully');
    }
  });
});
