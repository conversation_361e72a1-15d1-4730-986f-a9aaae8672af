<!DOCTYPE html>
<html>
<head>
    <title>Debug Notely Extension</title>
</head>
<body>
    <h1>Debug Notely Extension</h1>
    <p>Open the browser console (F12) and check for background script logs.</p>
    
    <h2>Steps to Debug:</h2>
    <ol>
        <li>Go to <code>chrome://extensions/</code></li>
        <li>Find the Notely extension</li>
        <li>Click "service worker" link to open background script console</li>
        <li>Look for logs starting with "[Background]"</li>
        <li>Reload the extension and check logs again</li>
    </ol>

    <h2>Expected Logs:</h2>
    <pre>
[Background] Background script loaded - creating context menus
[Background] Chrome contextMenus API available: true
[Background] Chrome contextMenus.create available: true
[Background] createContextMenus() called
[Background] Removing existing context menus...
[Background] Existing context menus removed
[Background] Creating text selection context menu...
[Background] Text menu created with ID: add-text-to-notely
[Background] Creating image context menu...
[Background] Image menu created with ID: add-image-to-notely
[Background] Creating page context menu...
[Background] Page menu created with ID: add-page-to-notely
[Background] ✅ All context menus created successfully
    </pre>

    <h2>Test Context Menu:</h2>
    <p>Right-click anywhere on this page to test if "Add to Notely" appears in the context menu.</p>
    
    <div style="margin: 20px; padding: 20px; border: 1px solid #ccc;">
        <p>Select this text and right-click to test text selection context menu.</p>
        <img src="https://via.placeholder.com/150" alt="Test image" style="display: block; margin: 10px 0;">
        <p>Right-click on the image above to test image context menu.</p>
    </div>

    <script>
        console.log('=== NOTELY EXTENSION DEBUG PAGE ===');
        console.log('Current URL:', window.location.href);
        console.log('User Agent:', navigator.userAgent);
        
        // Test if we can access chrome APIs (we shouldn't be able to from content page)
        console.log('Chrome runtime available:', typeof chrome !== 'undefined' && !!chrome.runtime);
        
        document.addEventListener('contextmenu', function(e) {
            console.log('Context menu triggered at:', e.clientX, e.clientY);
            console.log('Target element:', e.target);
        });
    </script>
</body>
</html>
