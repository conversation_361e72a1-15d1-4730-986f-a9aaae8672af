// Quick test script for settings functionality
// Copy and paste this into the browser console on the settings page

console.log('🔧 Quick Settings Test Starting...');

// Test 1: Check if Chrome APIs are available
if (typeof chrome === 'undefined' || !chrome.storage) {
    console.error('❌ Chrome extension APIs not available');
} else {
    console.log('✅ Chrome extension APIs available');
}

// Test 2: Check current storage state
async function checkCurrentState() {
    try {
        const local = await new Promise(resolve => {
            chrome.storage.local.get(['platformIntegrations', 'saveMediaToCloud', 'showMindstreamTips'], resolve);
        });
        
        const sync = await new Promise(resolve => {
            chrome.storage.sync.get(['locale'], resolve);
        });
        
        console.log('📦 Current local storage:', local);
        console.log('🌐 Current sync storage:', sync);
        
        return { local, sync };
    } catch (error) {
        console.error('❌ Error reading storage:', error);
        return null;
    }
}

// Test 3: Test platform toggle
async function testPlatformToggle() {
    console.log('\n🔘 Testing platform toggle...');
    
    try {
        // Get current state
        const current = await new Promise(resolve => {
            chrome.storage.local.get(['platformIntegrations'], resolve);
        });
        
        const currentIntegrations = current.platformIntegrations || {};
        console.log('Current platform integrations:', currentIntegrations);
        
        // Toggle LinkedIn (as an example)
        const newState = !currentIntegrations['LinkedIn'];
        const newIntegrations = {
            ...currentIntegrations,
            'LinkedIn': newState
        };
        
        // Save the change
        await new Promise(resolve => {
            chrome.storage.local.set({ platformIntegrations: newIntegrations }, resolve);
        });
        
        // Verify the change
        const updated = await new Promise(resolve => {
            chrome.storage.local.get(['platformIntegrations'], resolve);
        });
        
        console.log('✅ Platform toggle test completed');
        console.log('LinkedIn toggled to:', updated.platformIntegrations['LinkedIn']);
        
        // Restore original state
        setTimeout(async () => {
            await new Promise(resolve => {
                chrome.storage.local.set({ platformIntegrations: currentIntegrations }, resolve);
            });
            console.log('🔄 Original state restored');
        }, 2000);
        
        return true;
    } catch (error) {
        console.error('❌ Platform toggle test failed:', error);
        return false;
    }
}

// Test 4: Test language change
async function testLanguageChange() {
    console.log('\n🌐 Testing language change...');
    
    try {
        // Get current locale
        const current = await new Promise(resolve => {
            chrome.storage.sync.get(['locale'], resolve);
        });
        
        const currentLocale = current.locale || 'en';
        console.log('Current locale:', currentLocale);
        
        // Change to different locale
        const newLocale = currentLocale === 'en' ? 'tr' : 'en';
        
        await new Promise(resolve => {
            chrome.storage.sync.set({ locale: newLocale }, resolve);
        });
        
        // Verify the change
        const updated = await new Promise(resolve => {
            chrome.storage.sync.get(['locale'], resolve);
        });
        
        console.log('✅ Language change test completed');
        console.log('Locale changed to:', updated.locale);
        
        // Restore original locale
        setTimeout(async () => {
            await new Promise(resolve => {
                chrome.storage.sync.set({ locale: currentLocale }, resolve);
            });
            console.log('🔄 Original locale restored');
        }, 2000);
        
        return true;
    } catch (error) {
        console.error('❌ Language change test failed:', error);
        return false;
    }
}

// Test 5: Check UI elements
function testUIElements() {
    console.log('\n🎛️ Testing UI elements...');
    
    const languageSelect = document.querySelector('select');
    const toggles = document.querySelectorAll('[role="switch"]');
    const settingsCards = document.querySelectorAll('.bg-notely-dark-card');
    
    console.log('Language selector found:', !!languageSelect);
    console.log('Platform toggles found:', toggles.length);
    console.log('Settings cards found:', settingsCards.length);
    
    if (languageSelect) {
        console.log('Current language value:', languageSelect.value);
    }
    
    if (toggles.length > 0) {
        console.log('First toggle state:', toggles[0].getAttribute('aria-checked'));
    }
    
    return {
        languageSelect: !!languageSelect,
        toggleCount: toggles.length,
        cardCount: settingsCards.length
    };
}

// Test 6: Test actual UI interaction
function testUIInteraction() {
    console.log('\n🖱️ Testing UI interaction...');
    
    // Test language selector
    const languageSelect = document.querySelector('select');
    if (languageSelect) {
        console.log('Testing language selector click...');
        languageSelect.focus();
        console.log('✅ Language selector focused');
    }
    
    // Test first toggle
    const firstToggle = document.querySelector('[role="switch"]');
    if (firstToggle) {
        console.log('Testing platform toggle click...');
        const originalState = firstToggle.getAttribute('aria-checked');
        firstToggle.click();
        
        setTimeout(() => {
            const newState = firstToggle.getAttribute('aria-checked');
            console.log(`Toggle changed from ${originalState} to ${newState}`);
            
            if (originalState !== newState) {
                console.log('✅ Toggle interaction working');
            } else {
                console.log('⚠️ Toggle state did not change');
            }
        }, 500);
    }
    
    return true;
}

// Run all tests
async function runQuickTests() {
    console.log('🚀 Running quick settings tests...\n');
    
    // Basic checks
    const state = await checkCurrentState();
    const ui = testUIElements();
    
    // Functional tests
    const platformTest = await testPlatformToggle();
    const languageTest = await testLanguageChange();
    
    // Interaction test
    const interactionTest = testUIInteraction();
    
    console.log('\n📊 Test Results Summary:');
    console.log('Storage Access:', state ? '✅' : '❌');
    console.log('UI Elements:', ui.languageSelect && ui.toggleCount > 0 ? '✅' : '❌');
    console.log('Platform Toggle:', platformTest ? '✅' : '❌');
    console.log('Language Change:', languageTest ? '✅' : '❌');
    console.log('UI Interaction:', interactionTest ? '✅' : '❌');
    
    const allPassed = state && ui.languageSelect && ui.toggleCount > 0 && platformTest && languageTest && interactionTest;
    console.log('\n🎯 Overall Result:', allPassed ? '✅ ALL TESTS PASSED' : '⚠️ SOME ISSUES DETECTED');
    
    return {
        storageAccess: !!state,
        uiElements: ui.languageSelect && ui.toggleCount > 0,
        platformToggle: platformTest,
        languageChange: languageTest,
        uiInteraction: interactionTest,
        overall: allPassed
    };
}

// Make functions available globally
window.quickTest = {
    runQuickTests,
    checkCurrentState,
    testPlatformToggle,
    testLanguageChange,
    testUIElements,
    testUIInteraction
};

console.log('📋 Quick test functions loaded. Run window.quickTest.runQuickTests() to start testing.');

// Auto-run basic checks after a delay
setTimeout(() => {
    console.log('\n🔍 Auto-running basic checks...');
    checkCurrentState();
    testUIElements();
}, 1000);
