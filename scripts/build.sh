#!/bin/bash
set -e

# Clean up
echo "Cleaning up..."
rm -rf dist/
mkdir -p dist/assets/content

# Build content scripts
echo "Building content scripts..."
esbuild src/content/twitter-content.ts --bundle --format=iife --outfile=dist/twitter-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/linkedin-content.ts --bundle --format=iife --outfile=dist/linkedin-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/instagram-content.ts --bundle --format=iife --outfile=dist/instagram-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/reddit-content.ts --bundle --format=iife --outfile=dist/reddit-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/pinterest-content.ts --bundle --format=iife --outfile=dist/pinterest-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/web-content.ts --bundle --format=iife --outfile=dist/assets/content/web.js --target=esnext --sourcemap --allow-overwrite

# Build background script
echo "Building background script..."
esbuild src/background/background.ts --bundle --format=iife --outfile=dist/background.js --target=esnext --sourcemap --allow-overwrite --define:import.meta.env.VITE_OPENAI_API_KEY="\"$VITE_OPENAI_API_KEY\"" --define:process.env.OPENAI_API_KEY="\"$VITE_OPENAI_API_KEY\""

# Copy static assets
echo "Copying static assets..."
cp -r public/icons dist/
cp -r public/extension-icons dist/
cp public/popup.html dist/
mkdir -p dist/assets
cp public/assets/popup.js dist/assets/

# Build the rest with Vite
echo "Building with Vite..."
npx dotenv vite build

# Create the correct manifest.json directly in the dist directory AFTER Vite build
echo "Creating manifest.json..."
cat > dist/manifest.json << 'EOL'
{
  "manifest_version": 3,
  "name": "Notely.social",
  "version": "1.0.0",
  "description": "Save posts from social media platforms",
  "permissions": [
    "storage",
    "activeTab",
    "scripting",
    "tabs",
    "contextMenus",
    "identity",
    "webRequest"
  ],
  "host_permissions": [
    "*://*.twitter.com/*",
    "*://*.x.com/*",
    "*://*.linkedin.com/*",
    "*://*.instagram.com/*",
    "*://*.reddit.com/*",
    "*://*.pinterest.com/*",
    "http://*/*",
    "https://*/*"
  ],
  "background": {
    "service_worker": "background.js"
  },
  "action": {
    "default_icon": {
      "16": "extension-icons/icon16.png",
      "48": "extension-icons/icon48.png",
      "128": "extension-icons/icon128.png"
    }
  },
  "icons": {
    "16": "extension-icons/icon16.png",
    "48": "extension-icons/icon48.png",
    "128": "extension-icons/icon128.png"
  },
  "content_scripts": [
    {
      "matches": ["*://*.twitter.com/*", "*://*.x.com/*"],
      "js": ["twitter-content.js"]
    },
    {
      "matches": ["*://*.linkedin.com/*"],
      "js": ["linkedin-content.js"]
    },
    {
      "matches": ["*://*.instagram.com/*"],
      "js": ["instagram-content.js"]
    },
    {
      "matches": ["*://*.reddit.com/*"],
      "js": ["reddit-content.js"]
    },
    {
      "matches": ["*://*.pinterest.com/*"],
      "js": ["pinterest-content.js"]
    }
  ],
  "web_accessible_resources": [
    {
      "resources": ["icons/*", "assets/*"],
      "matches": ["<all_urls>"]
    }
  ]
}
EOL

echo "Build complete!"
